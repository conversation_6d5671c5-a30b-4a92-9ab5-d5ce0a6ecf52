'use client'

import { useState, useEffect, useRef } from 'react'

export default function ModalTestPage() {
  const [showModal, setShowModal] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [logs, setLogs] = useState<string[]>([])

  const addLog = (message: string) => {
    console.log(message)
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  // Test useEffect in modal
  useEffect(() => {
    addLog(`useEffect triggered: showModal=${showModal}, hasCanvasRef=${!!canvasRef.current}`)
    
    if (!showModal) {
      addLog('Modal not open, skipping')
      return
    }

    if (!canvasRef.current) {
      addLog('Canvas ref not available, skipping')
      return
    }

    addLog('All conditions met, starting test...')
    setIsLoading(true)

    // Simple test: just set loading to false after 2 seconds
    const timeout = setTimeout(() => {
      addLog('Setting isLoading to false...')
      setIsLoading(false)
      addLog('isLoading should now be false')
    }, 2000)

    return () => {
      addLog('useEffect cleanup')
      clearTimeout(timeout)
    }
  }, [showModal])

  const openModal = () => {
    addLog('Opening modal...')
    setShowModal(true)
    setIsLoading(true)
  }

  const closeModal = () => {
    addLog('Closing modal...')
    setShowModal(false)
    setLogs([])
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Modal Test</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Controls */}
          <div className="space-y-4">
            <button
              onClick={openModal}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Open Test Modal
            </button>
            
            <div className="bg-gray-800 p-4 rounded">
              <h3 className="font-semibold mb-2">State:</h3>
              <div className="text-sm space-y-1">
                <div>Modal Open: {showModal ? 'Yes' : 'No'}</div>
                <div>Is Loading: {isLoading ? 'Yes' : 'No'}</div>
                <div>Canvas Ref: {canvasRef.current ? 'Available' : 'Not Available'}</div>
              </div>
            </div>
          </div>

          {/* Logs */}
          <div className="bg-gray-800 p-4 rounded">
            <h3 className="font-semibold mb-2">Debug Logs:</h3>
            <div className="bg-gray-900 p-4 rounded text-sm font-mono max-h-64 overflow-y-auto">
              {logs.map((log, index) => (
                <div key={index} className="mb-1 text-gray-300">
                  {log}
                </div>
              ))}
              {logs.length === 0 && (
                <div className="text-gray-500">No logs yet...</div>
              )}
            </div>
          </div>
        </div>

        {/* Simple Modal */}
        {showModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80">
            <div className="bg-gray-800 border border-gray-600 rounded-lg w-[800px] h-[600px] flex flex-col">
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-600">
                <h2 className="text-xl font-semibold">Test Modal</h2>
                <button
                  onClick={closeModal}
                  className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700"
                >
                  Close
                </button>
              </div>

              {/* Content */}
              <div className="flex-1 flex items-center justify-center p-4">
                {isLoading ? (
                  <div className="flex flex-col items-center space-y-4">
                    <div className="animate-spin h-8 w-8 border-2 border-green-500 border-t-transparent rounded-full" />
                    <span className="text-lg">Testing loading state...</span>
                    <div className="text-sm text-gray-400">
                      This should change to "Ready!" after 2 seconds
                    </div>
                  </div>
                ) : (
                  <div className="text-center">
                    <div className="text-green-400 text-2xl font-bold mb-4">
                      ✅ Ready!
                    </div>
                    <canvas
                      ref={canvasRef}
                      width={400}
                      height={300}
                      className="border border-gray-600 bg-white"
                    />
                    <div className="text-sm text-gray-400 mt-2">
                      Canvas element is now available
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
