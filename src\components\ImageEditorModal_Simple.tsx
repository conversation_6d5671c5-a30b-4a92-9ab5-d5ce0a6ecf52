'use client'

import { useState, useEffect, useRef } from 'react'
import { Button } from './ui/Button'
import { 
  X, 
  Download, 
  Save, 
  Type, 
  RotateCcw,
  Plus
} from 'lucide-react'

interface ImageEditorModalProps {
  isOpen: boolean
  onClose: () => void
  imageUrl: string
  onSave: (editedImageUrl: string) => void
}

export function ImageEditorModal({ isOpen, onClose, imageUrl, onSave }: ImageEditorModalProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [backgroundImage, setBackgroundImage] = useState<HTMLImageElement | null>(null)
  const [textElements, setTextElements] = useState<Array<{
    text: string
    x: number
    y: number
    fontSize: number
    color: string
    id: string
  }>>([])
  
  // Text controls
  const [textInput, setTextInput] = useState('')
  const [textColor, setTextColor] = useState('#ffffff')
  const [fontSize, setFontSize] = useState(40)

  // Load and draw image when modal opens
  useEffect(() => {
    if (!isOpen || !canvasRef.current) return

    const loadImage = async () => {
      try {
        console.log('🚀 === SIMPLE CANVAS EDITOR LOADING ===')
        console.log('Image URL:', imageUrl.substring(0, 100) + '...')
        
        setIsLoading(true)
        setError(null)

        const canvas = canvasRef.current!
        const ctx = canvas.getContext('2d')!
        
        // Set canvas size
        canvas.width = 800
        canvas.height = 450
        
        console.log('Canvas setup complete, loading image...')

        // Load image using reliable HTML Image method
        let finalImageUrl = imageUrl
        
        // Convert external URLs to blob for CORS
        if (!imageUrl.startsWith('data:')) {
          try {
            console.log('Converting to blob for CORS...')
            const response = await fetch(imageUrl, { mode: 'cors' })
            if (response.ok) {
              const blob = await response.blob()
              finalImageUrl = URL.createObjectURL(blob)
              console.log('✅ Blob URL created')
            }
          } catch (error) {
            console.log('Blob conversion failed, using direct URL:', error)
          }
        }

        const img = new Image()
        img.crossOrigin = 'anonymous'
        
        img.onload = () => {
          console.log('✅ Image loaded successfully:', {
            width: img.width,
            height: img.height
          })

          // Calculate scaling to fit canvas
          const scaleX = canvas.width / img.width
          const scaleY = canvas.height / img.height
          const scale = Math.min(scaleX, scaleY) * 0.9

          const scaledWidth = img.width * scale
          const scaledHeight = img.height * scale
          const x = (canvas.width - scaledWidth) / 2
          const y = (canvas.height - scaledHeight) / 2

          // Clear canvas and draw image
          ctx.clearRect(0, 0, canvas.width, canvas.height)
          ctx.fillStyle = '#000000'
          ctx.fillRect(0, 0, canvas.width, canvas.height)
          ctx.drawImage(img, x, y, scaledWidth, scaledHeight)

          setBackgroundImage(img)
          setIsLoading(false)
          
          console.log('🎉 Simple canvas editor ready!')

          // Clean up blob URL
          if (finalImageUrl !== imageUrl && finalImageUrl.startsWith('blob:')) {
            setTimeout(() => URL.revokeObjectURL(finalImageUrl), 1000)
          }
        }

        img.onerror = (error) => {
          console.error('❌ Image loading failed:', error)
          setError('Failed to load image')
          setIsLoading(false)
        }

        img.src = finalImageUrl

      } catch (error) {
        console.error('❌ Editor initialization failed:', error)
        setError(`Failed to initialize editor: ${error instanceof Error ? error.message : 'Unknown error'}`)
        setIsLoading(false)
      }
    }

    loadImage()

    // Cleanup
    return () => {
      setTextElements([])
      setBackgroundImage(null)
    }
  }, [isOpen, imageUrl])

  // Redraw canvas when text elements change
  useEffect(() => {
    if (!canvasRef.current || !backgroundImage || isLoading) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')!

    // Redraw background image
    const scaleX = canvas.width / backgroundImage.width
    const scaleY = canvas.height / backgroundImage.height
    const scale = Math.min(scaleX, scaleY) * 0.9

    const scaledWidth = backgroundImage.width * scale
    const scaledHeight = backgroundImage.height * scale
    const x = (canvas.width - scaledWidth) / 2
    const y = (canvas.height - scaledHeight) / 2

    ctx.clearRect(0, 0, canvas.width, canvas.height)
    ctx.fillStyle = '#000000'
    ctx.fillRect(0, 0, canvas.width, canvas.height)
    ctx.drawImage(backgroundImage, x, y, scaledWidth, scaledHeight)

    // Draw text elements
    textElements.forEach(textEl => {
      ctx.font = `${textEl.fontSize}px Arial`
      ctx.fillStyle = textEl.color
      ctx.textAlign = 'left'
      ctx.textBaseline = 'top'
      ctx.fillText(textEl.text, textEl.x, textEl.y)
    })
  }, [textElements, backgroundImage, isLoading])

  const addText = () => {
    if (!textInput.trim()) return

    const newText = {
      text: textInput,
      x: 100,
      y: 100,
      fontSize: fontSize,
      color: textColor,
      id: Date.now().toString()
    }

    setTextElements(prev => [...prev, newText])
    setTextInput('')
  }

  const clearText = () => {
    setTextElements([])
  }

  const handleSave = () => {
    if (!canvasRef.current) return

    try {
      const dataURL = canvasRef.current.toDataURL('image/png', 0.9)
      onSave(dataURL)
      onClose()
    } catch (error) {
      console.error('Error saving:', error)
      setError('Failed to save image')
    }
  }

  const handleDownload = () => {
    if (!canvasRef.current) return

    try {
      const dataURL = canvasRef.current.toDataURL('image/png', 0.9)
      const link = document.createElement('a')
      link.download = 'edited-image.png'
      link.href = dataURL
      link.click()
    } catch (error) {
      console.error('Error downloading:', error)
      setError('Failed to download image')
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm">
      <div className="bg-bg-secondary border border-border-primary rounded-lg w-[95vw] h-[90vh] max-w-6xl flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border-primary">
          <h2 className="text-xl font-semibold text-text-primary">Simple Image Editor</h2>
          <div className="flex items-center space-x-2">
            {!isLoading && !error && (
              <>
                <Button
                  onClick={handleDownload}
                  variant="secondary"
                  size="sm"
                  className="flex items-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>Download</span>
                </Button>
                <Button
                  onClick={handleSave}
                  size="sm"
                  className="flex items-center space-x-2 bg-green-600 hover:bg-green-700"
                >
                  <Save className="w-4 h-4" />
                  <span>Save</span>
                </Button>
              </>
            )}
            <Button
              onClick={onClose}
              variant="secondary"
              size="sm"
              className="flex items-center space-x-2"
            >
              <X className="w-4 h-4" />
              <span>Close</span>
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 flex">
          {/* Sidebar */}
          {!isLoading && !error && (
            <div className="w-80 border-r border-border-primary p-4 space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-text-primary flex items-center space-x-2">
                  <Type className="w-5 h-5" />
                  <span>Add Text</span>
                </h3>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Text
                  </label>
                  <input
                    type="text"
                    value={textInput}
                    onChange={(e) => setTextInput(e.target.value)}
                    placeholder="Enter text..."
                    className="w-full px-3 py-2 bg-bg-primary border border-border-primary rounded text-text-primary"
                    onKeyPress={(e) => e.key === 'Enter' && addText()}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Font Size: {fontSize}px
                  </label>
                  <input
                    type="range"
                    min="12"
                    max="100"
                    value={fontSize}
                    onChange={(e) => setFontSize(Number(e.target.value))}
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Color
                  </label>
                  <input
                    type="color"
                    value={textColor}
                    onChange={(e) => setTextColor(e.target.value)}
                    className="w-full h-10 rounded border border-border-primary"
                  />
                </div>

                <div className="flex space-x-2">
                  <Button
                    onClick={addText}
                    disabled={!textInput.trim()}
                    className="flex-1 flex items-center justify-center space-x-2"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Add Text</span>
                  </Button>
                  
                  <Button
                    onClick={clearText}
                    variant="secondary"
                    className="flex items-center space-x-2"
                  >
                    <RotateCcw className="w-4 h-4" />
                    <span>Clear</span>
                  </Button>
                </div>

                {textElements.length > 0 && (
                  <div className="text-sm text-text-secondary">
                    {textElements.length} text element(s) added
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Canvas Area */}
          <div className="flex-1 flex items-center justify-center p-4">
            {isLoading ? (
              <div className="flex flex-col items-center space-y-4">
                <div className="animate-spin h-8 w-8 border-2 border-green-500 border-t-transparent rounded-full" />
                <span className="text-lg text-text-primary">Loading simple editor...</span>
              </div>
            ) : error ? (
              <div className="flex flex-col items-center space-y-4">
                <div className="text-red-400 text-center">
                  <h3 className="text-lg font-semibold mb-2">Error Loading Editor</h3>
                  <p className="text-sm mb-4">{error}</p>
                </div>
                <Button onClick={onClose} variant="secondary">
                  Close
                </Button>
              </div>
            ) : (
              <canvas
                ref={canvasRef}
                className="border border-border-primary rounded bg-black max-w-full max-h-full"
                style={{ width: '800px', height: '450px' }}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
