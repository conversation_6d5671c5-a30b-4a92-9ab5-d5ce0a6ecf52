'use client'

import { useState } from 'react'

export default function DebugLoadingPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [message, setMessage] = useState('Initial state')

  const testSetLoading = () => {
    console.log('🔄 Testing setIsLoading...')
    setMessage('Testing setIsLoading...')
    
    setTimeout(() => {
      console.log('⏰ Setting isLoading to false...')
      setIsLoading(false)
      setMessage('setIsLoading(false) called')
    }, 2000)
  }

  const resetTest = () => {
    console.log('🔄 Resetting test...')
    setIsLoading(true)
    setMessage('Reset - isLoading set to true')
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Debug Loading State</h1>
        
        <div className="space-y-6">
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">Loading State Test</h2>
            
            <div className="space-y-4">
              <div>
                <strong>Current isLoading:</strong> {isLoading ? 'true' : 'false'}
              </div>
              <div>
                <strong>Message:</strong> {message}
              </div>
              
              <div className="space-x-4">
                <button
                  onClick={testSetLoading}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Test setIsLoading(false)
                </button>
                
                <button
                  onClick={resetTest}
                  className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                >
                  Reset Test
                </button>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">Conditional Rendering Test</h2>
            
            {isLoading ? (
              <div className="flex flex-col items-center space-y-4">
                <div className="animate-spin h-8 w-8 border-2 border-green-500 border-t-transparent rounded-full" />
                <span className="text-lg">Loading state is TRUE - you should see this spinner</span>
              </div>
            ) : (
              <div className="text-green-400">
                <h3 className="text-lg font-semibold mb-2">✅ Loading Complete!</h3>
                <p>Loading state is FALSE - you should see this success message</p>
              </div>
            )}
          </div>

          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">Manual State Control</h2>
            
            <div className="space-x-4">
              <button
                onClick={() => {
                  console.log('Setting isLoading to true')
                  setIsLoading(true)
                  setMessage('Manually set to true')
                }}
                className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
              >
                Set Loading TRUE
              </button>
              
              <button
                onClick={() => {
                  console.log('Setting isLoading to false')
                  setIsLoading(false)
                  setMessage('Manually set to false')
                }}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                Set Loading FALSE
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
