'use client'

import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from './ui/Button'
import { X, Download, Save } from 'lucide-react'

// Import Toast UI Image Editor CSS
import 'tui-image-editor/dist/tui-image-editor.css'

// Toast UI Image Editor types
interface ToastUIImageEditor {
  loadImageFromURL: (url: string, imageName: string) => Promise<any>
  toDataURL: () => string
  destroy: () => void
  ui: {
    resizeEditor: (dimensions?: { width?: number; height?: number }) => void
  }
}

interface ImageEditorModalProps {
  isOpen: boolean
  onClose: () => void
  imageUrl: string
  onSave: (editedImageUrl: string) => void
}

export function ImageEditorModal({ isOpen, onClose, imageUrl, onSave }: ImageEditorModalProps) {
  const editorRef = useRef<HTMLDivElement>(null)
  const editorInstanceRef = useRef<ToastUIImageEditor | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [ToastUIImageEditor, setToastUIImageEditor] = useState<any>(null)

  // Load Toast UI Image Editor dynamically
  useEffect(() => {
    const loadToastUI = async () => {
      try {
        console.log('Loading Toast UI Image Editor...')

        // Dynamic import to avoid SSR issues
        const module = await import('tui-image-editor')
        console.log('Module imported:', module)

        // Try different ways to get the constructor
        const ImageEditor = module.default || module.ImageEditor || module
        console.log('ImageEditor constructor:', typeof ImageEditor)

        if (typeof ImageEditor !== 'function') {
          throw new Error('ImageEditor constructor not found')
        }

        setToastUIImageEditor(() => ImageEditor)

      } catch (error) {
        console.error('Failed to load Toast UI Image Editor:', error)
        setError(`Failed to load image editor: ${error instanceof Error ? error.message : 'Unknown error'}`)
        setIsLoading(false)
      }
    }

    if (!ToastUIImageEditor) {
      loadToastUI()
    }
  }, [ToastUIImageEditor])

  // Initialize editor when modal opens
  useEffect(() => {
    if (!isOpen || !ToastUIImageEditor || !editorRef.current) return

    const initializeEditor = async () => {
      try {
        console.log('Initializing Toast UI Image Editor...')
        setIsLoading(true)
        setError(null)

        // Clear any existing editor
        if (editorInstanceRef.current) {
          editorInstanceRef.current.destroy()
          editorInstanceRef.current = null
        }

        // Clear the container
        if (editorRef.current) {
          editorRef.current.innerHTML = ''
        }

        // Create new editor instance with simplified config
        const editor = new ToastUIImageEditor(editorRef.current, {
          includeUI: {
            loadImage: {
              path: imageUrl,
              name: 'EditImage'
            },
            menu: ['crop', 'flip', 'rotate', 'draw', 'shape', 'text', 'filter'],
            initMenu: 'filter',
            uiSize: {
              width: '100%',
              height: '500px'
            },
            menuBarPosition: 'bottom'
          },
          cssMaxWidth: 800,
          cssMaxHeight: 500
        })

        editorInstanceRef.current = editor

        // Wait for image to load
        console.log('Loading image into editor...')
        
        // The image should load automatically from the loadImage config
        // Wait a bit for it to initialize
        setTimeout(() => {
          setIsLoading(false)
          console.log('Toast UI Image Editor initialized successfully')
        }, 1000)

      } catch (error) {
        console.error('Error initializing Toast UI Image Editor:', error)
        setError(`Failed to initialize editor: ${error instanceof Error ? error.message : 'Unknown error'}`)
        setIsLoading(false)
      }
    }

    initializeEditor()

    // Cleanup on unmount or modal close
    return () => {
      if (editorInstanceRef.current) {
        try {
          editorInstanceRef.current.destroy()
        } catch (e) {
          console.warn('Error destroying editor:', e)
        }
        editorInstanceRef.current = null
      }
    }
  }, [isOpen, ToastUIImageEditor, imageUrl])

  const handleSave = () => {
    if (!editorInstanceRef.current) return

    try {
      console.log('Saving edited image...')
      const dataURL = editorInstanceRef.current.toDataURL()
      onSave(dataURL)
      onClose()
    } catch (error) {
      console.error('Error saving image:', error)
      setError('Failed to save image. Please try again.')
    }
  }

  const handleDownload = () => {
    if (!editorInstanceRef.current) return

    try {
      const dataURL = editorInstanceRef.current.toDataURL()
      const link = document.createElement('a')
      link.download = 'edited-image.png'
      link.href = dataURL
      link.click()
    } catch (error) {
      console.error('Error downloading image:', error)
      setError('Failed to download image. Please try again.')
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm">
      <div className="bg-bg-secondary border border-border-primary rounded-lg w-[95vw] h-[90vh] max-w-6xl flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border-primary">
          <h2 className="text-xl font-semibold text-text-primary">Image Editor</h2>
          <div className="flex items-center space-x-2">
            {!isLoading && !error && (
              <>
                <Button
                  onClick={handleDownload}
                  variant="secondary"
                  size="sm"
                  className="flex items-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>Download</span>
                </Button>
                <Button
                  onClick={handleSave}
                  size="sm"
                  className="flex items-center space-x-2 bg-green-600 hover:bg-green-700"
                >
                  <Save className="w-4 h-4" />
                  <span>Save</span>
                </Button>
              </>
            )}
            <Button
              onClick={onClose}
              variant="secondary"
              size="sm"
              className="flex items-center space-x-2"
            >
              <X className="w-4 h-4" />
              <span>Close</span>
            </Button>
          </div>
        </div>

        {/* Editor Content */}
        <div className="flex-1 p-4">
          {isLoading ? (
            <div className="flex flex-col items-center justify-center h-full space-y-4">
              <div className="animate-spin h-8 w-8 border-2 border-green-500 border-t-transparent rounded-full" />
              <span className="text-lg text-text-primary">Loading image editor...</span>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center h-full space-y-4">
              <div className="text-red-400 text-center">
                <h3 className="text-lg font-semibold mb-2">Error Loading Editor</h3>
                <p className="text-sm mb-4">{error}</p>
              </div>
              <Button onClick={onClose} variant="secondary">
                Close
              </Button>
            </div>
          ) : (
            <div 
              ref={editorRef}
              className="w-full h-full"
              style={{ minHeight: '500px' }}
            />
          )}
        </div>
      </div>
    </div>
  )
}
