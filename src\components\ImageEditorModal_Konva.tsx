'use client'

import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from './ui/Button'
import { 
  X, 
  Download, 
  Save, 
  Type, 
  Palette, 
  RotateCcw,
  Undo,
  Plus
} from 'lucide-react'

interface ImageEditorModalProps {
  isOpen: boolean
  onClose: () => void
  imageUrl: string
  onSave: (editedImageUrl: string) => void
}

export function ImageEditorModal({ isOpen, onClose, imageUrl, onSave }: ImageEditorModalProps) {
  const stageRef = useRef<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [Konva, setKonva] = useState<any>(null)
  const [activeTab, setActiveTab] = useState<'text' | 'filters'>('text')
  const [textInput, setTextInput] = useState('')
  const [textColor, setTextColor] = useState('#ffffff')
  const [fontSize, setFontSize] = useState(40)

  // Load Konva dynamically
  useEffect(() => {
    const loadKonva = async () => {
      try {
        console.log('Loading Konva...')
        const konvaModule = await import('konva')
        const konvaInstance = konvaModule.default
        console.log('Konva loaded successfully:', typeof konvaInstance)
        setKonva(konvaInstance)
      } catch (error) {
        console.error('Failed to load Konva:', error)
        setError('Failed to load image editor. Please refresh the page.')
        setIsLoading(false)
      }
    }

    if (!Konva) {
      loadKonva()
    }
  }, [Konva])

  // Initialize Konva stage when modal opens
  useEffect(() => {
    console.log('🔄 useEffect triggered with:', {
      isOpen,
      hasKonva: !!Konva,
      hasStageRef: !!stageRef.current,
      imageUrl: imageUrl.substring(0, 50) + '...'
    })

    if (!isOpen) {
      console.log('❌ Modal not open, skipping initialization')
      return
    }

    if (!Konva) {
      console.log('❌ Konva not loaded yet, skipping initialization')
      return
    }

    if (!stageRef.current) {
      console.log('❌ Stage ref not available, skipping initialization')
      return
    }

    console.log('✅ All conditions met, proceeding with initialization')

    const initializeEditor = async () => {
      try {
        console.log('🔥 === EDITOR INITIALIZATION START ===')
        console.log('Props received:', { isOpen, imageUrl: imageUrl.substring(0, 100) + '...' })
        console.log('Konva available:', !!Konva)
        console.log('Stage ref available:', !!stageRef.current)

        setIsLoading(true)
        setError(null)

        // Create stage
        console.log('📋 Creating Konva stage...')
        const stage = new Konva.Stage({
          container: stageRef.current,
          width: 800,
          height: 450
        })
        console.log('✅ Stage created successfully')

        // Create layer
        console.log('📋 Creating Konva layer...')
        const layer = new Konva.Layer()
        stage.add(layer)
        console.log('✅ Layer created and added to stage')

        // RELIABLE IMAGE LOADING - Use proven HTML Image approach
        console.log('🚀 Loading image with reliable HTML Image method...')
        console.log('Image URL type:', imageUrl.startsWith('data:') ? 'base64' : 'external')

        // Step 1: Load image using reliable HTML Image (we know this works from debug tests)
        const loadImageReliably = async () => {
          let finalImageUrl = imageUrl

          // For external URLs, convert to blob first (we know this works)
          if (!imageUrl.startsWith('data:')) {
            try {
              console.log('Converting external URL to blob...')
              const response = await fetch(imageUrl, { mode: 'cors' })
              if (response.ok) {
                const blob = await response.blob()
                finalImageUrl = URL.createObjectURL(blob)
                console.log('✅ Blob URL created successfully')
              }
            } catch (error) {
              console.log('Blob conversion failed, using direct URL:', error)
              // Keep original URL as fallback
            }
          }

          // Step 2: Load with HTML Image (guaranteed to work based on our tests)
          return new Promise<HTMLImageElement>((resolve, reject) => {
            const img = new Image()
            img.crossOrigin = 'anonymous'

            const timeout = setTimeout(() => {
              reject(new Error('Image loading timeout'))
            }, 10000)

            img.onload = () => {
              clearTimeout(timeout)
              console.log('✅ HTML Image loaded successfully:', {
                width: img.width,
                height: img.height
              })
              resolve(img)
            }

            img.onerror = (error) => {
              clearTimeout(timeout)
              reject(new Error(`Image loading failed: ${error}`))
            }

            img.src = finalImageUrl

            // Clean up blob URL if we created one
            if (finalImageUrl !== imageUrl && finalImageUrl.startsWith('blob:')) {
              setTimeout(() => URL.revokeObjectURL(finalImageUrl), 5000)
            }
          })
        }

        // Step 3: Use the loaded image with Konva
        try {
          const loadedImage = await loadImageReliably()

          console.log('Creating Konva image from loaded HTML image...')
          const konvaImage = new Konva.Image({
            x: 0,
            y: 0,
            image: loadedImage,
            width: 800,
            height: 450
          })

          // Scale image to fit canvas
          const scaleX = 800 / loadedImage.width
          const scaleY = 450 / loadedImage.height
          const scale = Math.min(scaleX, scaleY) * 0.9

          konvaImage.scale({ x: scale, y: scale })
          konvaImage.position({
            x: (800 - loadedImage.width * scale) / 2,
            y: (450 - loadedImage.height * scale) / 2
          })

          layer.add(konvaImage)
          layer.draw()

          setIsLoading(false)
          console.log('🎉 === EDITOR INITIALIZATION COMPLETE ===')
          console.log('Image editor loaded successfully!')

        } catch (error) {
          console.error('❌ === EDITOR INITIALIZATION FAILED ===')
          console.error('Error details:', error)
          setError(`Failed to load image: ${error instanceof Error ? error.message : 'Unknown error'}`)
          setIsLoading(false)
        }

        // Store stage reference for later use
        stageRef.current.konvaStage = stage

      } catch (error) {
        console.error('❌ === OUTER INITIALIZATION ERROR ===')
        console.error('Outer error details:', error)
        console.error('Stack trace:', error instanceof Error ? error.stack : 'No stack trace')
        setError(`Failed to initialize editor: ${error instanceof Error ? error.message : 'Unknown error'}`)
        setIsLoading(false)
      }
    }

    initializeEditor()

    // Cleanup
    return () => {
      if (stageRef.current?.konvaStage) {
        stageRef.current.konvaStage.destroy()
        stageRef.current.konvaStage = null
      }
    }
  }, [isOpen, Konva, imageUrl])

  const addText = () => {
    if (!textInput.trim() || !stageRef.current?.konvaStage || !Konva) return

    const stage = stageRef.current.konvaStage
    const layer = stage.getLayers()[0]

    const text = new Konva.Text({
      x: 100,
      y: 100,
      text: textInput,
      fontSize: fontSize,
      fontFamily: 'Arial',
      fill: textColor,
      draggable: true
    })

    // Add transformer for text editing
    const transformer = new Konva.Transformer()
    layer.add(text)
    layer.add(transformer)
    transformer.nodes([text])
    
    layer.draw()
    setTextInput('')
  }

  const handleSave = () => {
    if (!stageRef.current?.konvaStage) return

    try {
      console.log('Saving edited image...')
      const stage = stageRef.current.konvaStage
      const dataURL = stage.toDataURL({ quality: 0.9 })
      onSave(dataURL)
      onClose()
    } catch (error) {
      console.error('Error saving image:', error)
      setError('Failed to save image. Please try again.')
    }
  }

  const handleDownload = () => {
    if (!stageRef.current?.konvaStage) return

    try {
      const stage = stageRef.current.konvaStage
      const dataURL = stage.toDataURL({ quality: 0.9 })
      const link = document.createElement('a')
      link.download = 'edited-image.png'
      link.href = dataURL
      link.click()
    } catch (error) {
      console.error('Error downloading image:', error)
      setError('Failed to download image. Please try again.')
    }
  }

  const clearCanvas = () => {
    if (!stageRef.current?.konvaStage) return
    
    const stage = stageRef.current.konvaStage
    const layer = stage.getLayers()[0]
    
    // Keep only the background image (first child)
    const children = layer.getChildren()
    for (let i = children.length - 1; i > 0; i--) {
      children[i].destroy()
    }
    layer.draw()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm">
      <div className="bg-bg-secondary border border-border-primary rounded-lg w-[95vw] h-[90vh] max-w-6xl flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border-primary">
          <h2 className="text-xl font-semibold text-text-primary">Image Editor</h2>
          <div className="flex items-center space-x-2">
            {!isLoading && !error && (
              <>
                <Button
                  onClick={handleDownload}
                  variant="secondary"
                  size="sm"
                  className="flex items-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>Download</span>
                </Button>
                <Button
                  onClick={handleSave}
                  size="sm"
                  className="flex items-center space-x-2 bg-green-600 hover:bg-green-700"
                >
                  <Save className="w-4 h-4" />
                  <span>Save</span>
                </Button>
              </>
            )}
            <Button
              onClick={onClose}
              variant="secondary"
              size="sm"
              className="flex items-center space-x-2"
            >
              <X className="w-4 h-4" />
              <span>Close</span>
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 flex">
          {/* Sidebar */}
          {!isLoading && !error && (
            <div className="w-80 border-r border-border-primary p-4 space-y-6">
              {/* Tabs */}
              <div className="flex space-x-2">
                <Button
                  onClick={() => setActiveTab('text')}
                  variant={activeTab === 'text' ? 'default' : 'secondary'}
                  size="sm"
                  className="flex items-center space-x-2"
                >
                  <Type className="w-4 h-4" />
                  <span>Text</span>
                </Button>
                <Button
                  onClick={clearCanvas}
                  variant="secondary"
                  size="sm"
                  className="flex items-center space-x-2"
                >
                  <RotateCcw className="w-4 h-4" />
                  <span>Clear</span>
                </Button>
              </div>

              {/* Text Tab */}
              {activeTab === 'text' && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Text
                    </label>
                    <input
                      type="text"
                      value={textInput}
                      onChange={(e) => setTextInput(e.target.value)}
                      placeholder="Enter text..."
                      className="w-full px-3 py-2 bg-bg-primary border border-border-primary rounded text-text-primary"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Font Size: {fontSize}px
                    </label>
                    <input
                      type="range"
                      min="12"
                      max="100"
                      value={fontSize}
                      onChange={(e) => setFontSize(Number(e.target.value))}
                      className="w-full"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Color
                    </label>
                    <input
                      type="color"
                      value={textColor}
                      onChange={(e) => setTextColor(e.target.value)}
                      className="w-full h-10 rounded border border-border-primary"
                    />
                  </div>

                  <Button
                    onClick={addText}
                    disabled={!textInput.trim()}
                    className="w-full flex items-center justify-center space-x-2"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Add Text</span>
                  </Button>
                </div>
              )}
            </div>
          )}

          {/* Canvas Area */}
          <div className="flex-1 flex items-center justify-center p-4">
            {isLoading ? (
              <div className="flex flex-col items-center space-y-4">
                <div className="animate-spin h-8 w-8 border-2 border-green-500 border-t-transparent rounded-full" />
                <span className="text-lg text-text-primary">Loading image editor...</span>
              </div>
            ) : error ? (
              <div className="flex flex-col items-center space-y-4">
                <div className="text-red-400 text-center">
                  <h3 className="text-lg font-semibold mb-2">Error Loading Editor</h3>
                  <p className="text-sm mb-4">{error}</p>
                </div>
                <Button onClick={onClose} variant="secondary">
                  Close
                </Button>
              </div>
            ) : (
              <div 
                ref={stageRef}
                className="border border-border-primary rounded bg-gray-900"
                style={{ width: '800px', height: '450px' }}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
