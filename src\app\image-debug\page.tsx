'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'

const testImages = [
  {
    name: '<PERSON><PERSON> (CORS enabled)',
    url: 'https://picsum.photos/800/450'
  },
  {
    name: 'Unsplash (CORS enabled)',
    url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=450&fit=crop'
  },
  {
    name: 'Base64 Test Image',
    url: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
  }
]

export default function ImageDebugPage() {
  const [testResults, setTestResults] = useState<Record<string, any>>({})
  const [currentTestUrl, setCurrentTestUrl] = useState('')

  const testImageUrl = async (url: string, name: string) => {
    console.log(`🧪 Testing image URL: ${name}`)
    console.log(`URL: ${url}`)
    
    const results: any = {
      url,
      name,
      timestamp: new Date().toISOString()
    }

    // Test 1: Basic HTML Image
    try {
      const img = new Image()
      img.crossOrigin = 'anonymous'
      
      const htmlImagePromise = new Promise<boolean>((resolve) => {
        const timeout = setTimeout(() => {
          resolve(false)
        }, 10000)
        
        img.onload = () => {
          clearTimeout(timeout)
          results.htmlImage = {
            success: true,
            width: img.width,
            height: img.height,
            naturalWidth: img.naturalWidth,
            naturalHeight: img.naturalHeight
          }
          resolve(true)
        }
        
        img.onerror = (error) => {
          clearTimeout(timeout)
          results.htmlImage = {
            success: false,
            error: error.toString()
          }
          resolve(false)
        }
        
        img.src = url
      })
      
      await htmlImagePromise
    } catch (error) {
      results.htmlImage = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }

    // Test 2: Fetch test
    try {
      const fetchStart = Date.now()
      const response = await fetch(url, { 
        mode: 'cors',
        method: 'HEAD' // Just check headers, don't download full image
      })
      const fetchTime = Date.now() - fetchStart
      
      results.fetch = {
        success: response.ok,
        status: response.status,
        statusText: response.statusText,
        time: fetchTime,
        headers: Object.fromEntries(response.headers.entries())
      }
    } catch (error) {
      results.fetch = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }

    // Test 3: Blob conversion test
    if (!url.startsWith('data:')) {
      try {
        const response = await fetch(url, { mode: 'cors' })
        if (response.ok) {
          const blob = await response.blob()
          const blobUrl = URL.createObjectURL(blob)
          
          results.blobConversion = {
            success: true,
            blobType: blob.type,
            blobSize: blob.size,
            blobUrl: blobUrl.substring(0, 50) + '...'
          }
          
          // Clean up
          setTimeout(() => URL.revokeObjectURL(blobUrl), 1000)
        } else {
          results.blobConversion = {
            success: false,
            error: `HTTP ${response.status}: ${response.statusText}`
          }
        }
      } catch (error) {
        results.blobConversion = {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }
    }

    setTestResults(prev => ({ ...prev, [name]: results }))
    console.log(`✅ Test completed for ${name}:`, results)
  }

  const testCustomUrl = async () => {
    if (!currentTestUrl.trim()) return
    
    const customName = `Custom URL (${new Date().toLocaleTimeString()})`
    await testImageUrl(currentTestUrl.trim(), customName)
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Image URL Debug Tool</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Test Controls */}
          <div className="space-y-6">
            <div className="bg-gray-800 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4">Predefined Tests</h2>
              <div className="space-y-3">
                {testImages.map((image, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                    <div>
                      <div className="font-medium">{image.name}</div>
                      <div className="text-sm text-gray-400 break-all">{image.url.substring(0, 60)}...</div>
                    </div>
                    <Button
                      onClick={() => testImageUrl(image.url, image.name)}
                      size="sm"
                      className="ml-4"
                    >
                      Test
                    </Button>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4">Custom URL Test</h2>
              <div className="space-y-3">
                <input
                  type="text"
                  value={currentTestUrl}
                  onChange={(e) => setCurrentTestUrl(e.target.value)}
                  placeholder="Enter image URL to test..."
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"
                />
                <Button
                  onClick={testCustomUrl}
                  disabled={!currentTestUrl.trim()}
                  className="w-full"
                >
                  Test Custom URL
                </Button>
              </div>
            </div>
          </div>

          {/* Results */}
          <div className="bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4">Test Results</h2>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {Object.entries(testResults).map(([name, result]) => (
                <div key={name} className="bg-gray-700 p-4 rounded">
                  <h3 className="font-semibold mb-2">{name}</h3>
                  <div className="text-sm space-y-2">
                    <div>
                      <span className="font-medium">HTML Image: </span>
                      <span className={result.htmlImage?.success ? 'text-green-400' : 'text-red-400'}>
                        {result.htmlImage?.success ? '✅ Success' : '❌ Failed'}
                      </span>
                      {result.htmlImage?.success && (
                        <span className="text-gray-400 ml-2">
                          ({result.htmlImage.width}x{result.htmlImage.height})
                        </span>
                      )}
                    </div>
                    
                    <div>
                      <span className="font-medium">Fetch: </span>
                      <span className={result.fetch?.success ? 'text-green-400' : 'text-red-400'}>
                        {result.fetch?.success ? '✅ Success' : '❌ Failed'}
                      </span>
                      {result.fetch?.success && (
                        <span className="text-gray-400 ml-2">
                          ({result.fetch.status} in {result.fetch.time}ms)
                        </span>
                      )}
                    </div>
                    
                    {result.blobConversion && (
                      <div>
                        <span className="font-medium">Blob Conversion: </span>
                        <span className={result.blobConversion?.success ? 'text-green-400' : 'text-red-400'}>
                          {result.blobConversion?.success ? '✅ Success' : '❌ Failed'}
                        </span>
                        {result.blobConversion?.success && (
                          <span className="text-gray-400 ml-2">
                            ({result.blobConversion.blobType}, {Math.round(result.blobConversion.blobSize / 1024)}KB)
                          </span>
                        )}
                      </div>
                    )}
                    
                    {(result.htmlImage?.error || result.fetch?.error || result.blobConversion?.error) && (
                      <div className="text-red-400 text-xs mt-2">
                        Errors: {[result.htmlImage?.error, result.fetch?.error, result.blobConversion?.error].filter(Boolean).join(', ')}
                      </div>
                    )}
                  </div>
                </div>
              ))}
              
              {Object.keys(testResults).length === 0 && (
                <div className="text-gray-500 text-center py-8">
                  No tests run yet. Click a test button to start.
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
