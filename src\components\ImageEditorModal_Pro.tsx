'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { Button } from './ui/Button'
import { 
  X, Download, Save, Type, Palette, RotateCw, Crop, 
  Plus, Minus, Undo, Redo, Move, Square, Circle,
  Brush, Eraser, Image as ImageIcon, Sliders
} from 'lucide-react'

interface ImageEditorModalProps {
  isOpen: boolean
  onClose: () => void
  imageUrl: string
  onSave: (editedImageUrl: string) => void
}

interface TextElement {
  id: string
  text: string
  x: number
  y: number
  fontSize: number
  color: string
  fontFamily: string
  fontWeight: string
  selected: boolean
}

interface DrawingPath {
  id: string
  points: { x: number; y: number }[]
  color: string
  width: number
  tool: 'brush' | 'eraser'
}

export function ImageEditorModal({ isOpen, onClose, imageUrl, onSave }: ImageEditorModalProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isReady, setIsReady] = useState(false)
  const [loadedImage, setLoadedImage] = useState<HTMLImageElement | null>(null)
  
  // Editor state
  const [activeTab, setActiveTab] = useState<'text' | 'draw' | 'filters' | 'shapes'>('text')
  const [history, setHistory] = useState<string[]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  
  // Text editing
  const [textElements, setTextElements] = useState<TextElement[]>([])
  const [textInput, setTextInput] = useState('')
  const [textColor, setTextColor] = useState('#ffffff')
  const [fontSize, setFontSize] = useState(40)
  const [fontFamily, setFontFamily] = useState('Arial')
  const [fontWeight, setFontWeight] = useState('normal')
  
  // Drawing
  const [drawingPaths, setDrawingPaths] = useState<DrawingPath[]>([])
  const [isDrawing, setIsDrawing] = useState(false)
  const [currentPath, setCurrentPath] = useState<DrawingPath | null>(null)
  const [brushColor, setBrushColor] = useState('#ff0000')
  const [brushSize, setBrushSize] = useState(5)
  const [drawingTool, setDrawingTool] = useState<'brush' | 'eraser'>('brush')
  
  // Filters
  const [filters, setFilters] = useState({
    brightness: 100,
    contrast: 100,
    saturation: 100,
    blur: 0,
    sepia: 0,
    grayscale: 0
  })
  
  // Simplified - no complex scaling needed

  // Load image (using proven method from working editor)
  useEffect(() => {
    if (!isOpen) {
      setIsReady(false)
      setLoadedImage(null)
      setTextElements([])
      setDrawingPaths([])
      setHistory([])
      setHistoryIndex(-1)
      return
    }

    const loadImage = async () => {
      console.log('🚀 Loading image for pro editor...')

      try {
        // Use the exact same method that worked in the simple editor
        const img = new Image()
        img.crossOrigin = 'anonymous'

        await new Promise<void>((resolve, reject) => {
          img.onload = () => {
            console.log('✅ Image loaded for pro editor:', img.width, 'x', img.height)
            setLoadedImage(img)
            setIsReady(true)
            resolve()
          }

          img.onerror = (error) => {
            console.error('❌ Image load failed:', error)
            reject(error)
          }

          img.src = imageUrl
        })

      } catch (error) {
        console.error('Failed to load image:', error)
        setIsReady(false)
      }
    }

    loadImage()
  }, [isOpen, imageUrl])

  // Save to history
  const saveToHistory = useCallback(() => {
    if (!canvasRef.current) return
    
    const dataURL = canvasRef.current.toDataURL()
    setHistory(prev => {
      const newHistory = prev.slice(0, historyIndex + 1)
      newHistory.push(dataURL)
      return newHistory.slice(-20) // Keep last 20 states
    })
    setHistoryIndex(prev => prev + 1)
  }, [historyIndex])

  // Undo/Redo
  const undo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(prev => prev - 1)
      restoreFromHistory(history[historyIndex - 1])
    }
  }

  const redo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(prev => prev + 1)
      restoreFromHistory(history[historyIndex + 1])
    }
  }

  const restoreFromHistory = (dataURL: string) => {
    const canvas = canvasRef.current
    if (!canvas) return
    
    const ctx = canvas.getContext('2d')
    if (!ctx) return
    
    const img = new Image()
    img.onload = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      ctx.drawImage(img, 0, 0)
    }
    img.src = dataURL
  }

  // Draw everything to canvas (simplified approach that works)
  const redrawCanvas = useCallback(() => {
    if (!isReady || !loadedImage || !canvasRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size (same as working editor)
    canvas.width = 800
    canvas.height = 450

    // Calculate image scaling (same as working editor)
    const scaleX = canvas.width / loadedImage.width
    const scaleY = canvas.height / loadedImage.height
    const scale = Math.min(scaleX, scaleY) * 0.9

    const scaledWidth = loadedImage.width * scale
    const scaledHeight = loadedImage.height * scale
    const x = (canvas.width - scaledWidth) / 2
    const y = (canvas.height - scaledHeight) / 2

    // Clear and draw background (same as working editor)
    ctx.fillStyle = '#000000'
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // Apply filters to image
    ctx.filter = `
      brightness(${filters.brightness}%)
      contrast(${filters.contrast}%)
      saturate(${filters.saturation}%)
      blur(${filters.blur}px)
      sepia(${filters.sepia}%)
      grayscale(${filters.grayscale}%)
    `

    // Draw background image
    ctx.drawImage(loadedImage, x, y, scaledWidth, scaledHeight)

    // Reset filter for other elements
    ctx.filter = 'none'

    // Draw drawing paths
    drawingPaths.forEach(path => {
      if (path.points.length < 2) return
      
      ctx.beginPath()
      ctx.strokeStyle = path.color
      ctx.lineWidth = path.width
      ctx.lineCap = 'round'
      ctx.lineJoin = 'round'
      
      if (path.tool === 'eraser') {
        ctx.globalCompositeOperation = 'destination-out'
      } else {
        ctx.globalCompositeOperation = 'source-over'
      }
      
      ctx.moveTo(path.points[0].x, path.points[0].y)
      for (let i = 1; i < path.points.length; i++) {
        ctx.lineTo(path.points[i].x, path.points[i].y)
      }
      ctx.stroke()
    })
    
    ctx.globalCompositeOperation = 'source-over'

    // Draw text elements
    textElements.forEach(textEl => {
      ctx.font = `${textEl.fontWeight} ${textEl.fontSize}px ${textEl.fontFamily}`
      ctx.fillStyle = textEl.color
      ctx.textAlign = 'left'
      ctx.textBaseline = 'top'
      
      // Add text shadow
      ctx.shadowColor = 'rgba(0, 0, 0, 0.8)'
      ctx.shadowBlur = 4
      ctx.shadowOffsetX = 2
      ctx.shadowOffsetY = 2
      
      ctx.fillText(textEl.text, textEl.x, textEl.y)
      
      // Selection indicator
      if (textEl.selected) {
        ctx.shadowColor = 'transparent'
        ctx.shadowBlur = 0
        ctx.shadowOffsetX = 0
        ctx.shadowOffsetY = 0
        
        const metrics = ctx.measureText(textEl.text)
        ctx.strokeStyle = '#00ff00'
        ctx.lineWidth = 2
        ctx.strokeRect(textEl.x - 2, textEl.y - 2, metrics.width + 4, textEl.fontSize + 4)
      }
      
      // Reset shadow
      ctx.shadowColor = 'transparent'
      ctx.shadowBlur = 0
      ctx.shadowOffsetX = 0
      ctx.shadowOffsetY = 0
    })
  }, [isReady, loadedImage, textElements, drawingPaths, filters])

  // Redraw when anything changes
  useEffect(() => {
    redrawCanvas()
  }, [redrawCanvas])

  // Mouse events for drawing
  const handleMouseDown = (e: React.MouseEvent) => {
    if (activeTab !== 'draw') return
    
    const canvas = canvasRef.current
    if (!canvas) return
    
    const rect = canvas.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top
    
    const newPath: DrawingPath = {
      id: Date.now().toString(),
      points: [{ x, y }],
      color: drawingTool === 'eraser' ? '#000000' : brushColor,
      width: brushSize,
      tool: drawingTool
    }
    
    setCurrentPath(newPath)
    setIsDrawing(true)
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDrawing || !currentPath || activeTab !== 'draw') return
    
    const canvas = canvasRef.current
    if (!canvas) return
    
    const rect = canvas.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top
    
    setCurrentPath(prev => prev ? {
      ...prev,
      points: [...prev.points, { x, y }]
    } : null)
  }

  const handleMouseUp = () => {
    if (isDrawing && currentPath) {
      setDrawingPaths(prev => [...prev, currentPath])
      setCurrentPath(null)
      setIsDrawing(false)
      setTimeout(saveToHistory, 100)
    }
  }

  // Add text
  const addText = () => {
    if (!textInput.trim()) return

    const newText: TextElement = {
      id: Date.now().toString(),
      text: textInput,
      x: 100 + (textElements.length * 20),
      y: 100 + (textElements.length * 50),
      fontSize: fontSize,
      color: textColor,
      fontFamily: fontFamily,
      fontWeight: fontWeight,
      selected: false
    }

    setTextElements(prev => [...prev, newText])
    setTextInput('')
    setTimeout(saveToHistory, 100)
  }

  // Clear functions
  const clearText = () => {
    setTextElements([])
    setTimeout(saveToHistory, 100)
  }

  const clearDrawing = () => {
    setDrawingPaths([])
    setTimeout(saveToHistory, 100)
  }

  const resetFilters = () => {
    setFilters({
      brightness: 100,
      contrast: 100,
      saturation: 100,
      blur: 0,
      sepia: 0,
      grayscale: 0
    })
  }

  // Save and download
  const handleSave = () => {
    if (!canvasRef.current) return

    try {
      const dataURL = canvasRef.current.toDataURL('image/png', 0.9)
      console.log('💾 Saving edited image...')
      onSave(dataURL)
      onClose()
    } catch (error) {
      console.error('Error saving:', error)
    }
  }

  const handleDownload = () => {
    if (!canvasRef.current) return

    try {
      const dataURL = canvasRef.current.toDataURL('image/png', 0.9)
      const link = document.createElement('a')
      link.download = 'edited-image.png'
      link.href = dataURL
      link.click()
    } catch (error) {
      console.error('Error downloading:', error)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/90 backdrop-blur-sm">
      <div className="bg-bg-secondary border border-border-primary rounded-lg w-[98vw] h-[95vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border-primary">
          <h2 className="text-xl font-semibold text-text-primary">Professional Image Editor</h2>
          <div className="flex items-center space-x-2">
            {isReady && (
              <>
                <Button onClick={undo} disabled={historyIndex <= 0} variant="secondary" size="sm">
                  <Undo className="w-4 h-4" />
                </Button>
                <Button onClick={redo} disabled={historyIndex >= history.length - 1} variant="secondary" size="sm">
                  <Redo className="w-4 h-4" />
                </Button>
                <Button onClick={handleDownload} variant="secondary" size="sm">
                  <Download className="w-4 h-4" />
                  <span>Download</span>
                </Button>
                <Button onClick={handleSave} size="sm" className="bg-green-600 hover:bg-green-700">
                  <Save className="w-4 h-4" />
                  <span>Save</span>
                </Button>
              </>
            )}
            <Button onClick={onClose} variant="secondary" size="sm">
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <div className="flex-1 flex">
          {/* Sidebar */}
          {isReady && (
            <div className="w-80 border-r border-border-primary p-4 space-y-4 overflow-y-auto">
              {/* Tab Navigation */}
              <div className="flex space-x-1 bg-bg-primary rounded p-1">
                {[
                  { id: 'text', icon: Type, label: 'Text' },
                  { id: 'draw', icon: Brush, label: 'Draw' },
                  { id: 'filters', icon: Sliders, label: 'Filters' },
                ].map(tab => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded transition-colors ${
                      activeTab === tab.id 
                        ? 'bg-green-600 text-white' 
                        : 'text-text-secondary hover:text-text-primary'
                    }`}
                  >
                    <tab.icon className="w-4 h-4" />
                    <span className="text-sm">{tab.label}</span>
                  </button>
                ))}
              </div>

              {/* Tab Content */}
              {activeTab === 'text' && (
                <div className="space-y-4">
                  <h3 className="font-semibold text-text-primary">Text Tools</h3>
                  
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">Text</label>
                    <input
                      type="text"
                      value={textInput}
                      onChange={(e) => setTextInput(e.target.value)}
                      placeholder="Enter text..."
                      className="w-full px-3 py-2 bg-bg-primary border border-border-primary rounded text-text-primary"
                      onKeyPress={(e) => e.key === 'Enter' && addText()}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">Size: {fontSize}px</label>
                      <input
                        type="range"
                        min="12"
                        max="120"
                        value={fontSize}
                        onChange={(e) => setFontSize(Number(e.target.value))}
                        className="w-full"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">Color</label>
                      <input
                        type="color"
                        value={textColor}
                        onChange={(e) => setTextColor(e.target.value)}
                        className="w-full h-10 rounded border border-border-primary"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">Font</label>
                      <select
                        value={fontFamily}
                        onChange={(e) => setFontFamily(e.target.value)}
                        className="w-full px-3 py-2 bg-bg-primary border border-border-primary rounded text-text-primary"
                      >
                        <option value="Arial">Arial</option>
                        <option value="Helvetica">Helvetica</option>
                        <option value="Times New Roman">Times</option>
                        <option value="Georgia">Georgia</option>
                        <option value="Verdana">Verdana</option>
                        <option value="Impact">Impact</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">Weight</label>
                      <select
                        value={fontWeight}
                        onChange={(e) => setFontWeight(e.target.value)}
                        className="w-full px-3 py-2 bg-bg-primary border border-border-primary rounded text-text-primary"
                      >
                        <option value="normal">Normal</option>
                        <option value="bold">Bold</option>
                      </select>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Button onClick={addText} disabled={!textInput.trim()} className="flex-1">
                      <Plus className="w-4 h-4 mr-2" />
                      Add Text
                    </Button>
                    <Button onClick={clearText} variant="secondary">
                      Clear All
                    </Button>
                  </div>

                  {textElements.length > 0 && (
                    <div className="text-sm text-text-secondary">
                      {textElements.length} text element(s)
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'draw' && (
                <div className="space-y-4">
                  <h3 className="font-semibold text-text-primary">Drawing Tools</h3>
                  
                  <div className="flex space-x-2">
                    <Button
                      onClick={() => setDrawingTool('brush')}
                      variant={drawingTool === 'brush' ? 'default' : 'secondary'}
                      className="flex-1"
                    >
                      <Brush className="w-4 h-4 mr-2" />
                      Brush
                    </Button>
                    <Button
                      onClick={() => setDrawingTool('eraser')}
                      variant={drawingTool === 'eraser' ? 'default' : 'secondary'}
                      className="flex-1"
                    >
                      <Eraser className="w-4 h-4 mr-2" />
                      Eraser
                    </Button>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">
                      Brush Size: {brushSize}px
                    </label>
                    <input
                      type="range"
                      min="1"
                      max="50"
                      value={brushSize}
                      onChange={(e) => setBrushSize(Number(e.target.value))}
                      className="w-full"
                    />
                  </div>

                  {drawingTool === 'brush' && (
                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">Brush Color</label>
                      <input
                        type="color"
                        value={brushColor}
                        onChange={(e) => setBrushColor(e.target.value)}
                        className="w-full h-10 rounded border border-border-primary"
                      />
                    </div>
                  )}

                  <Button onClick={clearDrawing} variant="secondary" className="w-full">
                    Clear Drawing
                  </Button>

                  {drawingPaths.length > 0 && (
                    <div className="text-sm text-text-secondary">
                      {drawingPaths.length} drawing path(s)
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'filters' && (
                <div className="space-y-4">
                  <h3 className="font-semibold text-text-primary">Image Filters</h3>
                  
                  {Object.entries(filters).map(([key, value]) => (
                    <div key={key}>
                      <label className="block text-sm font-medium text-text-primary mb-2 capitalize">
                        {key}: {value}{key === 'brightness' || key === 'contrast' || key === 'saturation' ? '%' : key === 'blur' ? 'px' : '%'}
                      </label>
                      <input
                        type="range"
                        min={key === 'blur' ? 0 : key === 'brightness' || key === 'contrast' || key === 'saturation' ? 0 : 0}
                        max={key === 'blur' ? 20 : key === 'brightness' || key === 'contrast' || key === 'saturation' ? 200 : 100}
                        value={value}
                        onChange={(e) => setFilters(prev => ({ ...prev, [key]: Number(e.target.value) }))}
                        className="w-full"
                      />
                    </div>
                  ))}

                  <Button onClick={resetFilters} variant="secondary" className="w-full">
                    Reset Filters
                  </Button>
                </div>
              )}
            </div>
          )}

          {/* Canvas Area */}
          <div className="flex-1 flex items-center justify-center p-4 bg-gray-800">
            {!isReady ? (
              <div className="flex flex-col items-center space-y-4">
                <div className="animate-spin h-8 w-8 border-2 border-green-500 border-t-transparent rounded-full" />
                <span className="text-lg text-text-primary">Loading professional editor...</span>
              </div>
            ) : (
              <canvas
                ref={canvasRef}
                className="border border-border-primary rounded bg-black cursor-crosshair max-w-full max-h-full"
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseUp}
                style={{ width: '800px', height: '450px' }}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
