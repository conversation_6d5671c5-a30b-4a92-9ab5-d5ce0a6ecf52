'use client'

import { useState, useEffect } from 'react'

interface ImageEditorModalProps {
  isOpen: boolean
  onClose: () => void
  imageUrl: string
  onSave: (editedImageUrl: string) => void
}

export function ImageEditorModal({ isOpen, onClose, imageUrl, onSave }: ImageEditorModalProps) {
  const [step, setStep] = useState(0)
  const [logs, setLogs] = useState<string[]>([])

  const addLog = (message: string) => {
    console.log(message)
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  // Reset when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      addLog('=== MODAL OPENED ===')
      addLog(`Image URL: ${imageUrl}`)
      setStep(0)
      setLogs([])
      
      // Start the step-by-step process
      setTimeout(() => {
        addLog('Step 1: Modal is open')
        setStep(1)
        
        setTimeout(() => {
          addLog('Step 2: Starting image load test')
          setStep(2)
          
          // Test image loading
          const img = new Image()
          img.crossOrigin = 'anonymous'
          
          img.onload = () => {
            addLog('Step 3: Image loaded successfully!')
            addLog(`Image size: ${img.width}x${img.height}`)
            setStep(3)
            
            setTimeout(() => {
              addLog('Step 4: Editor ready!')
              setStep(4)
            }, 1000)
          }
          
          img.onerror = (error) => {
            addLog('Step 3: Image load failed!')
            addLog(`Error: ${error}`)
            setStep(-1) // Error state
          }
          
          addLog('Setting image src...')
          img.src = imageUrl
          
        }, 1000)
      }, 500)
    } else {
      addLog('Modal closed')
      setStep(0)
      setLogs([])
    }
  }, [isOpen, imageUrl])

  if (!isOpen) return null

  const getStepMessage = () => {
    switch (step) {
      case 0: return 'Initializing...'
      case 1: return 'Modal opened, preparing...'
      case 2: return 'Testing image load...'
      case 3: return 'Image loaded, setting up editor...'
      case 4: return 'Editor ready!'
      case -1: return 'Error occurred'
      default: return 'Unknown step'
    }
  }

  const getStepColor = () => {
    switch (step) {
      case 4: return 'text-green-400'
      case -1: return 'text-red-400'
      default: return 'text-yellow-400'
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm">
      <div className="bg-gray-800 border border-gray-600 rounded-lg w-[90vw] h-[80vh] max-w-4xl flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-600">
          <h2 className="text-xl font-semibold text-white">Debug Image Editor</h2>
          <button
            onClick={onClose}
            className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Close
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 p-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Status */}
          <div className="space-y-4">
            <div className="bg-gray-900 p-4 rounded">
              <h3 className="text-lg font-semibold text-white mb-2">Current Status</h3>
              <div className={`text-lg ${getStepColor()}`}>
                Step {step >= 0 ? step : 'Error'}: {getStepMessage()}
              </div>
            </div>

            <div className="bg-gray-900 p-4 rounded">
              <h3 className="text-lg font-semibold text-white mb-2">Progress</h3>
              <div className="space-y-2">
                {[1, 2, 3, 4].map(stepNum => (
                  <div key={stepNum} className="flex items-center space-x-2">
                    <div className={`w-4 h-4 rounded-full ${
                      step >= stepNum ? 'bg-green-500' : 
                      step === stepNum - 1 ? 'bg-yellow-500' : 'bg-gray-600'
                    }`} />
                    <span className={`${
                      step >= stepNum ? 'text-green-400' : 
                      step === stepNum - 1 ? 'text-yellow-400' : 'text-gray-400'
                    }`}>
                      Step {stepNum}: {
                        stepNum === 1 ? 'Modal Open' :
                        stepNum === 2 ? 'Image Load Test' :
                        stepNum === 3 ? 'Image Loaded' :
                        'Editor Ready'
                      }
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {step === 4 && (
              <div className="bg-green-900 p-4 rounded border border-green-600">
                <h3 className="text-lg font-semibold text-green-400 mb-2">✅ Success!</h3>
                <p className="text-green-300 mb-4">
                  The image editor would normally be ready now. This proves the loading logic works.
                </p>
                <button
                  onClick={() => {
                    onSave('data:image/png;base64,test')
                    onClose()
                  }}
                  className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                >
                  Simulate Save
                </button>
              </div>
            )}

            {step === -1 && (
              <div className="bg-red-900 p-4 rounded border border-red-600">
                <h3 className="text-lg font-semibold text-red-400 mb-2">❌ Error</h3>
                <p className="text-red-300">
                  Image failed to load. This is the root cause of the "keeps loading" issue.
                </p>
              </div>
            )}
          </div>

          {/* Debug Logs */}
          <div className="bg-gray-900 p-4 rounded">
            <h3 className="text-lg font-semibold text-white mb-2">Debug Logs</h3>
            <div className="bg-black p-4 rounded text-sm font-mono max-h-96 overflow-y-auto">
              {logs.map((log, index) => (
                <div key={index} className="mb-1 text-gray-300">
                  {log}
                </div>
              ))}
              {logs.length === 0 && (
                <div className="text-gray-500">No logs yet...</div>
              )}
            </div>
          </div>
        </div>

        {/* Image URL Info */}
        <div className="p-4 border-t border-gray-600 bg-gray-900">
          <div className="text-sm text-gray-400">
            <strong>Image URL:</strong> {imageUrl.substring(0, 100)}
            {imageUrl.length > 100 && '...'}
          </div>
          <div className="text-sm text-gray-400 mt-1">
            <strong>URL Type:</strong> {imageUrl.startsWith('data:') ? 'Base64 Data URL' : 'External HTTP URL'}
          </div>
        </div>
      </div>
    </div>
  )
}
