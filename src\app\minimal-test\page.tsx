'use client'

import { useState, useRef } from 'react'

export default function MinimalTest() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [status, setStatus] = useState('Ready')
  const [logs, setLogs] = useState<string[]>([])

  const addLog = (message: string) => {
    console.log(message)
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const testImageLoad = async (imageUrl: string) => {
    addLog('=== MINIMAL IMAGE LOAD TEST ===')
    addLog(`Testing URL: ${imageUrl}`)
    
    if (!canvasRef.current) {
      addLog('❌ Canvas ref not available')
      return
    }

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    
    if (!ctx) {
      addLog('❌ Canvas context not available')
      return
    }

    addLog('✅ Canvas and context available')
    
    // Clear canvas
    canvas.width = 400
    canvas.height = 300
    ctx.fillStyle = '#f0f0f0'
    ctx.fillRect(0, 0, canvas.width, canvas.height)
    addLog('✅ Canvas cleared and sized')

    setStatus('Loading image...')

    try {
      // Test 1: Direct image load
      addLog('📋 Creating HTML Image element...')
      const img = new Image()
      img.crossOrigin = 'anonymous'
      
      const loadPromise = new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          addLog('⏰ Image loading timeout (10s)')
          reject(new Error('Timeout'))
        }, 10000)

        img.onload = () => {
          clearTimeout(timeout)
          addLog(`✅ Image loaded: ${img.width}x${img.height}`)
          
          try {
            // Draw to canvas
            ctx.drawImage(img, 0, 0, 200, 150)
            addLog('✅ Image drawn to canvas')
            setStatus('Image loaded successfully!')
            resolve()
          } catch (drawError) {
            addLog(`❌ Canvas draw error: ${drawError}`)
            reject(drawError)
          }
        }

        img.onerror = (error) => {
          clearTimeout(timeout)
          addLog(`❌ Image load error: ${error}`)
          reject(error)
        }

        addLog('📋 Setting image src...')
        img.src = imageUrl
        addLog('📋 Image src set, waiting...')
      })

      await loadPromise

    } catch (error) {
      addLog(`❌ Test failed: ${error}`)
      setStatus('Test failed')
    }
  }

  const testUrls = [
    'https://picsum.photos/400/300',
    'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop',
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
  ]

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Minimal Image Load Test</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Controls */}
          <div className="space-y-4">
            <div className="bg-gray-800 p-4 rounded">
              <h2 className="text-xl font-semibold mb-4">Test Controls</h2>
              <div className="space-y-2">
                {testUrls.map((url, index) => (
                  <button
                    key={index}
                    onClick={() => testImageLoad(url)}
                    className="w-full p-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-left text-sm"
                  >
                    Test {index + 1}: {url.substring(0, 50)}...
                  </button>
                ))}
              </div>
            </div>

            <div className="bg-gray-800 p-4 rounded">
              <h3 className="font-semibold mb-2">Status: {status}</h3>
              <canvas
                ref={canvasRef}
                width={400}
                height={300}
                className="border border-gray-600 bg-white"
              />
            </div>
          </div>

          {/* Logs */}
          <div className="bg-gray-800 p-4 rounded">
            <h2 className="text-xl font-semibold mb-4">Debug Logs</h2>
            <div className="bg-gray-900 p-4 rounded text-sm font-mono max-h-96 overflow-y-auto">
              {logs.map((log, index) => (
                <div key={index} className="mb-1 text-gray-300">
                  {log}
                </div>
              ))}
              {logs.length === 0 && (
                <div className="text-gray-500">No logs yet...</div>
              )}
            </div>
            <button
              onClick={() => setLogs([])}
              className="mt-2 px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 text-sm"
            >
              Clear Logs
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
