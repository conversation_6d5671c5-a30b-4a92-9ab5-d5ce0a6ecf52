'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { Button } from './ui/Button'
import { 
  X, 
  Type, 
  Palette, 
  Image as ImageIcon, 
  Sparkles, 
  Download, 
  RotateCcw, 
  Undo, 
  Save,
  Upload,
  Sliders,
  Wand2,
  Plus
} from 'lucide-react'
// Fabric.js will be loaded dynamically

interface ImageEditorModalProps {
  isOpen: boolean
  onClose: () => void
  imageUrl: string
  onSave: (editedImageUrl: string) => void
}

interface FilterSettings {
  brightness: number
  contrast: number
  saturation: number
  blur: number
  sepia: boolean
  grayscale: boolean
}

interface TextSettings {
  text: string
  fontSize: number
  fontFamily: string
  color: string
  fontWeight: string
}

export function ImageEditorModal({ isOpen, onClose, imageUrl, onSave }: ImageEditorModalProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fabricCanvasRef = useRef<any>(null)
  const [activeTab, setActiveTab] = useState<'text' | 'filters' | 'logo' | 'ai'>('text')
  const [isLoading, setIsLoading] = useState(true)
  const [canvasError, setCanvasError] = useState<string | null>(null)
  const [fabric, setFabric] = useState<any>(null)
  const [history, setHistory] = useState<string[]>([])
  const [historyIndex, setHistoryIndex] = useState(-1)
  
  // Filter settings
  const [filters, setFilters] = useState<FilterSettings>({
    brightness: 0,
    contrast: 0,
    saturation: 0,
    blur: 0,
    sepia: false,
    grayscale: false
  })
  
  // Text settings
  const [textSettings, setTextSettings] = useState<TextSettings>({
    text: 'Your Text Here',
    fontSize: 40,
    fontFamily: 'Arial',
    color: '#ffffff',
    fontWeight: 'bold'
  })
  
  // AI editing
  const [aiPrompt, setAiPrompt] = useState('')
  const [isProcessingAI, setIsProcessingAI] = useState(false)
  const [selectedArea, setSelectedArea] = useState<any>(null)
  const [isSelecting, setIsSelecting] = useState(false)

  const saveToHistory = useCallback(() => {
    if (!fabricCanvasRef.current) return

    const canvasState = JSON.stringify(fabricCanvasRef.current.toJSON())
    setHistory(prev => {
      const newHistory = prev.slice(0, historyIndex + 1)
      newHistory.push(canvasState)
      return newHistory
    })
    setHistoryIndex(prev => prev + 1)
  }, [historyIndex])

  // Load Fabric.js dynamically
  useEffect(() => {
    const loadFabric = async () => {
      try {
        console.log('Loading Fabric.js dynamically...')
        const fabricModule = await import('fabric')
        const fabricInstance = fabricModule.fabric
        console.log('Fabric.js loaded successfully:', typeof fabricInstance)
        setFabric(fabricInstance)
      } catch (error) {
        console.error('Failed to load Fabric.js:', error)
        setCanvasError('Failed to load image editor library. Please refresh the page.')
        setIsLoading(false)
      }
    }

    if (!fabric) {
      loadFabric()
    }
  }, [fabric])

  // Initialize Fabric.js canvas
  useEffect(() => {
    if (!isOpen || !canvasRef.current || !fabric) return

    console.log('Initializing canvas...')
    console.log('Fabric available:', typeof fabric)
    console.log('Image URL to load:', imageUrl)
    console.log('Image URL type:', imageUrl.startsWith('data:') ? 'base64' : 'http')

    try {
      const canvas = new fabric.Canvas(canvasRef.current, {
        width: 800,
        height: 450,
        backgroundColor: '#000000'
      })

      if (!canvas) {
        throw new Error('Failed to create Fabric.js canvas')
      }

      console.log('Canvas created successfully:', canvas)
      fabricCanvasRef.current = canvas

      // Enhanced image loading with better error handling and CORS support
      const loadImage = async () => {
        console.log('Loading image:', imageUrl.substring(0, 100) + '...')
        setIsLoading(true)
        setCanvasError(null)

        try {
          let imageToLoad = imageUrl

          // First, test if the image URL is accessible
          console.log('Testing image accessibility...')
          const testImg = new Image()
          testImg.crossOrigin = 'anonymous'

          const imageTestPromise = new Promise<boolean>((resolve) => {
            testImg.onload = () => {
              console.log('Image test successful - image is accessible')
              resolve(true)
            }
            testImg.onerror = () => {
              console.log('Image test failed - image not accessible directly')
              resolve(false)
            }
            testImg.src = imageUrl
          })

          const isDirectlyAccessible = await imageTestPromise

          // If it's not a data URL and not directly accessible, we need to handle CORS properly
          if (!imageUrl.startsWith('data:') && !isDirectlyAccessible) {
            console.log('Converting external URL to blob for CORS handling...')
            console.log('Original URL:', imageUrl)

            try {
              // First try with CORS mode
              const response = await fetch(imageUrl, {
                mode: 'cors',
                credentials: 'omit',
                headers: {
                  'Accept': 'image/*'
                }
              })

              if (!response.ok) {
                throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`)
              }

              const blob = await response.blob()
              imageToLoad = URL.createObjectURL(blob)
              console.log('Created blob URL:', imageToLoad.substring(0, 50) + '...')
              console.log('Blob type:', blob.type, 'size:', blob.size)

            } catch (corsError) {
              console.warn('CORS fetch failed, trying no-cors mode:', corsError)

              // Fallback: try no-cors mode (limited but might work)
              try {
                const response = await fetch(imageUrl, {
                  mode: 'no-cors'
                })

                const blob = await response.blob()
                imageToLoad = URL.createObjectURL(blob)
                console.log('Created blob URL with no-cors:', imageToLoad.substring(0, 50) + '...')

              } catch (noCorsError) {
                console.warn('No-cors fetch also failed, using direct URL:', noCorsError)
                // Use direct URL as last resort
                imageToLoad = imageUrl
              }
            }
          } else if (!imageUrl.startsWith('data:') && isDirectlyAccessible) {
            console.log('Image is directly accessible, using original URL')
          }

          // Set a timeout for image loading
          const loadingTimeout = setTimeout(() => {
            console.error('Image loading timeout after 15 seconds')
            setCanvasError('Image loading timed out. Please try again.')
            setIsLoading(false)
          }, 15000)

          // Use Fabric.js built-in image loading with enhanced error handling
          const loadFabricImage = (urlToTry: string, isRetry: boolean = false) => {
            console.log(`Loading fabric image from: ${urlToTry.substring(0, 50)}... (retry: ${isRetry})`)

            fabric.Image.fromURL(urlToTry, (img: any) => {
              clearTimeout(loadingTimeout)

              if (!img || !canvas) {
                console.error('Failed to create fabric image or canvas not available')

                // If this was the first attempt and we used a blob URL, try the original URL
                if (!isRetry && urlToTry !== imageUrl) {
                  console.log('Retrying with original URL...')
                  loadFabricImage(imageUrl, true)
                  return
                }

                setCanvasError('Failed to load image into editor')
                setIsLoading(false)
                return
              }

              console.log('Image loaded successfully:', {
                width: img.width,
                height: img.height,
                src: img.getSrc().substring(0, 50) + '...'
              })

              // Scale image to fit canvas while maintaining aspect ratio
              const canvasWidth = canvas.getWidth()
              const canvasHeight = canvas.getHeight()
              const imgWidth = img.width || 1
              const imgHeight = img.height || 1

              const scale = Math.min(canvasWidth / imgWidth, canvasHeight / imgHeight) * 0.9 // 90% to leave some padding

              img.scale(scale)
              img.set({
                left: (canvasWidth - imgWidth * scale) / 2,
                top: (canvasHeight - imgHeight * scale) / 2,
                selectable: false,
                evented: false,
                name: 'backgroundImage' // Add name for identification
              })

              canvas.add(img)
              canvas.sendToBack(img)
              canvas.renderAll()
              setIsLoading(false)

              // Save initial state
              saveToHistory()

              // Clean up blob URL if we created one
              if (imageToLoad !== imageUrl && imageToLoad.startsWith('blob:')) {
                setTimeout(() => URL.revokeObjectURL(imageToLoad), 1000)
              }
            }, {
              crossOrigin: 'anonymous'
            })
          }

          // Start loading with the processed URL
          loadFabricImage(imageToLoad)

        } catch (error) {
          console.error('Error in loadImage:', error)
          setCanvasError(`Failed to load image: ${error instanceof Error ? error.message : 'Unknown error'}`)
          setIsLoading(false)
        }
      }

      loadImage()

      return () => {
        if (canvas) {
          canvas.dispose()
        }
        fabricCanvasRef.current = null
      }
    } catch (error) {
      console.error('Error initializing canvas:', error)
      setCanvasError(`Canvas initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      setIsLoading(false)
    }
  }, [isOpen, imageUrl, saveToHistory, fabric])



  const undo = useCallback(() => {
    if (historyIndex <= 0 || !fabricCanvasRef.current) return
    
    const prevState = history[historyIndex - 1]
    fabricCanvasRef.current.loadFromJSON(prevState, () => {
      fabricCanvasRef.current?.renderAll()
    })
    setHistoryIndex(prev => prev - 1)
  }, [history, historyIndex])

  const addText = () => {
    if (!fabricCanvasRef.current || !fabric) return

    const text = new fabric.Text(textSettings.text, {
      left: 100,
      top: 100,
      fontSize: textSettings.fontSize,
      fontFamily: textSettings.fontFamily,
      fill: textSettings.color,
      fontWeight: textSettings.fontWeight,
      stroke: '#000000',
      strokeWidth: 2
    })

    fabricCanvasRef.current.add(text)
    fabricCanvasRef.current.setActiveObject(text)
    fabricCanvasRef.current.renderAll()
    saveToHistory()
  }

  const applyFilters = () => {
    if (!fabricCanvasRef.current || !fabric) return

    const objects = fabricCanvasRef.current.getObjects()
    const backgroundImage = objects.find(obj => obj.type === 'image')

    if (!backgroundImage) return

    const filterArray: any[] = []

    if (filters.brightness !== 0) {
      filterArray.push(new fabric.Image.filters.Brightness({ brightness: filters.brightness / 100 }))
    }

    if (filters.contrast !== 0) {
      filterArray.push(new fabric.Image.filters.Contrast({ contrast: filters.contrast / 100 }))
    }

    if (filters.saturation !== 0) {
      filterArray.push(new fabric.Image.filters.Saturation({ saturation: filters.saturation / 100 }))
    }

    if (filters.blur > 0) {
      filterArray.push(new fabric.Image.filters.Blur({ blur: filters.blur / 10 }))
    }

    if (filters.sepia) {
      filterArray.push(new fabric.Image.filters.Sepia())
    }

    if (filters.grayscale) {
      filterArray.push(new fabric.Image.filters.Grayscale())
    }

    ;(backgroundImage as any).filters = filterArray
    ;(backgroundImage as any).applyFilters()
    fabricCanvasRef.current.renderAll()
    saveToHistory()
  }

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file || !fabricCanvasRef.current || !fabric) return

    const reader = new FileReader()
    reader.onload = (e) => {
      const result = e.target?.result as string
      fabric.Image.fromURL(result, (img: any) => {
        if (!img || !fabricCanvasRef.current) return

        img.scale(0.3)
        img.set({
          left: 50,
          top: 50
        })

        fabricCanvasRef.current.add(img)
        fabricCanvasRef.current.setActiveObject(img)
        fabricCanvasRef.current.renderAll()
        saveToHistory()
      })
    }
    reader.readAsDataURL(file)
  }

  const startAreaSelection = () => {
    if (!fabricCanvasRef.current) return
    
    setIsSelecting(true)
    fabricCanvasRef.current.defaultCursor = 'crosshair'
    
    let isDown = false
    let origX = 0
    let origY = 0
    let rect: any

    const onMouseDown = (o: any) => {
      if (!fabricCanvasRef.current) return
      
      isDown = true
      const pointer = fabricCanvasRef.current.getPointer(o.e)
      origX = pointer.x
      origY = pointer.y

      if (!fabric) return

      rect = new fabric.Rect({
        left: origX,
        top: origY,
        originX: 'left',
        originY: 'top',
        width: 0,
        height: 0,
        fill: 'rgba(255, 0, 0, 0.3)',
        stroke: '#ff0000',
        strokeWidth: 2,
        selectable: false
      })
      
      fabricCanvasRef.current.add(rect)
    }

    const onMouseMove = (o: any) => {
      if (!isDown || !fabricCanvasRef.current) return
      
      const pointer = fabricCanvasRef.current.getPointer(o.e)
      
      if (origX > pointer.x) {
        rect.set({ left: Math.abs(pointer.x) })
      }
      if (origY > pointer.y) {
        rect.set({ top: Math.abs(pointer.y) })
      }
      
      rect.set({ width: Math.abs(origX - pointer.x) })
      rect.set({ height: Math.abs(origY - pointer.y) })
      
      fabricCanvasRef.current.renderAll()
    }

    const onMouseUp = () => {
      if (!fabricCanvasRef.current) return
      
      isDown = false
      setSelectedArea(rect)
      setIsSelecting(false)
      fabricCanvasRef.current.defaultCursor = 'default'
      
      // Remove event listeners
      fabricCanvasRef.current.off('mouse:down', onMouseDown)
      fabricCanvasRef.current.off('mouse:move', onMouseMove)
      fabricCanvasRef.current.off('mouse:up', onMouseUp)
    }

    fabricCanvasRef.current.on('mouse:down', onMouseDown)
    fabricCanvasRef.current.on('mouse:move', onMouseMove)
    fabricCanvasRef.current.on('mouse:up', onMouseUp)
  }

  const processAIEdit = async () => {
    if (!aiPrompt.trim() || !selectedArea || !fabricCanvasRef.current) return

    setIsProcessingAI(true)

    try {
      // Get the current canvas as image data
      const canvasDataUrl = fabricCanvasRef.current.toDataURL({
        format: 'png',
        quality: 1
      })

      // Create mask data from selected area
      const maskCanvas = document.createElement('canvas')
      const maskCtx = maskCanvas.getContext('2d')

      if (!maskCtx) {
        throw new Error('Could not create mask canvas context')
      }

      // Set canvas size to match the fabric canvas
      maskCanvas.width = fabricCanvasRef.current.getWidth()
      maskCanvas.height = fabricCanvasRef.current.getHeight()

      // Fill with black (areas to keep)
      maskCtx.fillStyle = 'black'
      maskCtx.fillRect(0, 0, maskCanvas.width, maskCanvas.height)

      // Fill selected area with white (areas to inpaint)
      maskCtx.fillStyle = 'white'
      maskCtx.fillRect(
        selectedArea.left || 0,
        selectedArea.top || 0,
        selectedArea.width || 0,
        selectedArea.height || 0
      )

      const maskDataUrl = maskCanvas.toDataURL()

      // Call the AI inpainting API
      const response = await fetch('/api/ai-inpaint', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl: canvasDataUrl,
          maskData: maskDataUrl,
          prompt: aiPrompt,
          selectedArea: {
            x: selectedArea.left || 0,
            y: selectedArea.top || 0,
            width: selectedArea.width || 0,
            height: selectedArea.height || 0
          }
        })
      })

      const data = await response.json()

      if (data.success && data.imageUrl) {
        // Load the new image and replace the canvas content with CORS handling
        let imageToLoad = data.imageUrl

        // Handle CORS for external URLs
        if (!data.imageUrl.startsWith('data:')) {
          try {
            const response = await fetch(data.imageUrl, {
              mode: 'cors',
              credentials: 'omit'
            })

            if (response.ok) {
              const blob = await response.blob()
              imageToLoad = URL.createObjectURL(blob)
            }
          } catch (corsError) {
            console.warn('CORS fetch failed, trying direct URL:', corsError)
            // Fall back to direct URL
          }
        }

        if (!fabric) {
          console.error('Fabric.js not available for AI inpainting')
          return
        }

        fabric.Image.fromURL(imageToLoad, (img: any) => {
          if (!img || !fabricCanvasRef.current) return

          // Clear the canvas and add the new image
          fabricCanvasRef.current.clear()

          // Scale image to fit canvas
          const canvasWidth = fabricCanvasRef.current.getWidth()
          const canvasHeight = fabricCanvasRef.current.getHeight()
          const imgWidth = img.width || 1
          const imgHeight = img.height || 1

          const scale = Math.min(canvasWidth / imgWidth, canvasHeight / imgHeight) * 0.9

          img.scale(scale)
          img.set({
            left: (canvasWidth - imgWidth * scale) / 2,
            top: (canvasHeight - imgHeight * scale) / 2,
            selectable: false,
            evented: false,
            name: 'backgroundImage'
          })

          fabricCanvasRef.current.add(img)
          fabricCanvasRef.current.sendToBack(img)
          fabricCanvasRef.current.renderAll()

          saveToHistory()

          // Clean up blob URL if we created one
          if (imageToLoad !== data.imageUrl && imageToLoad.startsWith('blob:')) {
            setTimeout(() => URL.revokeObjectURL(imageToLoad), 1000)
          }
        }, {
          crossOrigin: 'anonymous',
          onError: (error: any) => {
            console.error('Failed to load AI inpainted image:', error)
            // Clean up blob URL if we created one
            if (imageToLoad !== data.imageUrl && imageToLoad.startsWith('blob:')) {
              URL.revokeObjectURL(imageToLoad)
            }
          }
        })
      } else {
        throw new Error(data.error || 'AI inpainting failed')
      }
    } catch (error) {
      console.error('AI inpainting error:', error)
      // You might want to show an error message to the user here
    } finally {
      setIsProcessingAI(false)
      if (selectedArea && fabricCanvasRef.current) {
        fabricCanvasRef.current.remove(selectedArea)
        fabricCanvasRef.current.renderAll()
        setSelectedArea(null)
      }
      setAiPrompt('')
    }
  }

  const handleSave = async () => {
    if (!fabricCanvasRef.current) return

    try {
      setIsLoading(true)

      // Export canvas as blob for better performance
      const canvas = fabricCanvasRef.current
      const dataURL = canvas.toDataURL({
        format: 'png',
        quality: 0.9, // Slightly reduce quality for smaller file size
        multiplier: 1 // Don't scale up
      })

      console.log('Saving edited image...')

      // For now, return the data URL (could be enhanced to upload to storage)
      onSave(dataURL)
      onClose()

    } catch (error) {
      console.error('Error saving image:', error)
      setCanvasError('Failed to save image. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDownload = () => {
    if (!fabricCanvasRef.current) return
    
    const dataURL = fabricCanvasRef.current.toDataURL({
      format: 'png',
      quality: 1
    })
    
    const link = document.createElement('a')
    link.download = 'edited-thumbnail.png'
    link.href = dataURL
    link.click()
  }

  const resetCanvas = () => {
    if (history.length > 0 && fabricCanvasRef.current) {
      const initialState = history[0]
      fabricCanvasRef.current.loadFromJSON(initialState, () => {
        fabricCanvasRef.current?.renderAll()
      })
      setHistoryIndex(0)
      setFilters({
        brightness: 0,
        contrast: 0,
        saturation: 0,
        blur: 0,
        sepia: false,
        grayscale: false
      })
    }
  }

  if (!isOpen) return null

  if (!imageUrl) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm">
        <div className="bg-bg-secondary border border-border-primary rounded-lg p-6">
          <p className="text-text-primary">No image URL provided</p>
          <Button onClick={onClose} className="mt-4">Close</Button>
        </div>
      </div>
    )
  }

  // Debug information (only log when there are issues)
  if (canvasError) {
    console.log('ImageEditorModal error:', {
      isOpen,
      imageUrlType: imageUrl.startsWith('data:') ? 'base64' : 'external',
      canvasError
    })
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm">
      <div className="relative mx-4 w-full max-w-6xl h-[90vh] rounded-lg bg-bg-secondary border border-border-primary overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-border-primary p-4">
          <div className="flex items-center space-x-3">
            <Palette className="h-5 w-5 text-accent-primary" />
            <h2 className="text-lg font-semibold text-text-primary">Image Editor</h2>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" onClick={undo} disabled={historyIndex <= 0}>
              <Undo className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={resetCanvas}>
              <RotateCcw className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex h-full">
          {/* Toolbar */}
          <div className="w-80 border-r border-border-primary p-4 overflow-y-auto bg-bg-primary">
            {/* Tool Tabs */}
            <div className="grid grid-cols-4 gap-1 mb-6">
              {[
                { id: 'text', icon: Type, label: 'Text' },
                { id: 'filters', icon: Sliders, label: 'Filters' },
                { id: 'logo', icon: ImageIcon, label: 'Logo' },
                { id: 'ai', icon: Wand2, label: 'AI Edit' }
              ].map((tab) => (
                <Button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  variant={activeTab === tab.id ? 'primary' : 'secondary'}
                  size="sm"
                  className="flex flex-col items-center p-2 h-auto"
                >
                  <tab.icon className="h-4 w-4 mb-1" />
                  <span className="text-xs">{tab.label}</span>
                </Button>
              ))}
            </div>

            {/* Tool Content */}
            <div className="space-y-4">
              {activeTab === 'text' && (
                <div className="space-y-4">
                  <h3 className="font-semibold text-text-primary">Add Text</h3>

                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-text-secondary mb-1">Text</label>
                      <input
                        type="text"
                        value={textSettings.text}
                        onChange={(e) => setTextSettings(prev => ({ ...prev, text: e.target.value }))}
                        className="w-full px-3 py-2 bg-bg-primary border border-border-primary rounded text-text-primary"
                        placeholder="Enter your text"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-secondary mb-1">Font Size</label>
                      <input
                        type="range"
                        min="12"
                        max="120"
                        value={textSettings.fontSize}
                        onChange={(e) => setTextSettings(prev => ({ ...prev, fontSize: parseInt(e.target.value) }))}
                        className="w-full"
                      />
                      <span className="text-xs text-text-tertiary">{textSettings.fontSize}px</span>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-secondary mb-1">Font Family</label>
                      <select
                        value={textSettings.fontFamily}
                        onChange={(e) => setTextSettings(prev => ({ ...prev, fontFamily: e.target.value }))}
                        className="w-full px-3 py-2 bg-bg-primary border border-border-primary rounded text-text-primary"
                      >
                        <option value="Arial">Arial</option>
                        <option value="Helvetica">Helvetica</option>
                        <option value="Times New Roman">Times New Roman</option>
                        <option value="Georgia">Georgia</option>
                        <option value="Verdana">Verdana</option>
                        <option value="Impact">Impact</option>
                        <option value="Comic Sans MS">Comic Sans MS</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-secondary mb-1">Color</label>
                      <input
                        type="color"
                        value={textSettings.color}
                        onChange={(e) => setTextSettings(prev => ({ ...prev, color: e.target.value }))}
                        className="w-full h-10 rounded border border-border-primary"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-secondary mb-1">Font Weight</label>
                      <select
                        value={textSettings.fontWeight}
                        onChange={(e) => setTextSettings(prev => ({ ...prev, fontWeight: e.target.value }))}
                        className="w-full px-3 py-2 bg-bg-primary border border-border-primary rounded text-text-primary"
                      >
                        <option value="normal">Normal</option>
                        <option value="bold">Bold</option>
                        <option value="bolder">Bolder</option>
                        <option value="lighter">Lighter</option>
                      </select>
                    </div>
                  </div>

                  <Button onClick={addText} className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Text
                  </Button>
                </div>
              )}

              {activeTab === 'filters' && (
                <div className="space-y-4">
                  <h3 className="font-semibold text-text-primary">Image Filters</h3>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-text-secondary mb-1">Brightness</label>
                      <input
                        type="range"
                        min="-100"
                        max="100"
                        value={filters.brightness}
                        onChange={(e) => setFilters(prev => ({ ...prev, brightness: parseInt(e.target.value) }))}
                        className="w-full"
                      />
                      <span className="text-xs text-text-tertiary">{filters.brightness}%</span>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-secondary mb-1">Contrast</label>
                      <input
                        type="range"
                        min="-100"
                        max="100"
                        value={filters.contrast}
                        onChange={(e) => setFilters(prev => ({ ...prev, contrast: parseInt(e.target.value) }))}
                        className="w-full"
                      />
                      <span className="text-xs text-text-tertiary">{filters.contrast}%</span>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-secondary mb-1">Saturation</label>
                      <input
                        type="range"
                        min="-100"
                        max="100"
                        value={filters.saturation}
                        onChange={(e) => setFilters(prev => ({ ...prev, saturation: parseInt(e.target.value) }))}
                        className="w-full"
                      />
                      <span className="text-xs text-text-tertiary">{filters.saturation}%</span>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-text-secondary mb-1">Blur</label>
                      <input
                        type="range"
                        min="0"
                        max="50"
                        value={filters.blur}
                        onChange={(e) => setFilters(prev => ({ ...prev, blur: parseInt(e.target.value) }))}
                        className="w-full"
                      />
                      <span className="text-xs text-text-tertiary">{filters.blur}px</span>
                    </div>

                    <div className="space-y-2">
                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={filters.sepia}
                          onChange={(e) => setFilters(prev => ({ ...prev, sepia: e.target.checked }))}
                          className="rounded"
                        />
                        <span className="text-sm text-text-secondary">Sepia</span>
                      </label>

                      <label className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={filters.grayscale}
                          onChange={(e) => setFilters(prev => ({ ...prev, grayscale: e.target.checked }))}
                          className="rounded"
                        />
                        <span className="text-sm text-text-secondary">Grayscale</span>
                      </label>
                    </div>
                  </div>

                  <Button onClick={applyFilters} className="w-full">
                    <Sliders className="h-4 w-4 mr-2" />
                    Apply Filters
                  </Button>
                </div>
              )}

              {activeTab === 'logo' && (
                <div className="space-y-4">
                  <h3 className="font-semibold text-text-primary">Add Logo</h3>

                  <div className="border-2 border-dashed border-border-primary rounded-lg p-6 text-center">
                    <Upload className="h-8 w-8 mx-auto mb-2 text-text-tertiary" />
                    <p className="text-sm text-text-secondary mb-2">Upload your logo</p>
                    <p className="text-xs text-text-tertiary mb-4">PNG, JPG, SVG supported</p>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      className="hidden"
                      id="logo-upload"
                    />
                    <label htmlFor="logo-upload">
                      <Button size="sm">
                        Choose File
                      </Button>
                    </label>
                  </div>

                  <div className="text-xs text-text-tertiary">
                    <p>• Drag and resize the logo after uploading</p>
                    <p>• Use corner handles to maintain aspect ratio</p>
                    <p>• Double-click to edit transparency</p>
                  </div>
                </div>
              )}

              {activeTab === 'ai' && (
                <div className="space-y-4">
                  <h3 className="font-semibold text-text-primary">AI Edit</h3>

                  <div className="space-y-3">
                    <div>
                      <p className="text-sm text-text-secondary mb-2">1. Select an area to edit</p>
                      <Button
                        onClick={startAreaSelection}
                        disabled={isSelecting}
                        variant="secondary"
                        className="w-full"
                      >
                        {isSelecting ? 'Selecting...' : 'Select Area'}
                      </Button>
                    </div>

                    {selectedArea && (
                      <div>
                        <label className="block text-sm font-medium text-text-secondary mb-1">
                          2. Describe what you want to add/change
                        </label>
                        <textarea
                          value={aiPrompt}
                          onChange={(e) => setAiPrompt(e.target.value)}
                          placeholder="e.g., add sunglasses, change background to beach, add fire effects..."
                          className="w-full px-3 py-2 bg-bg-primary border border-border-primary rounded text-text-primary resize-none"
                          rows={3}
                        />
                      </div>
                    )}

                    {selectedArea && (
                      <Button
                        onClick={processAIEdit}
                        disabled={!aiPrompt.trim() || isProcessingAI}
                        className="w-full"
                      >
                        {isProcessingAI ? (
                          <>
                            <div className="animate-spin h-4 w-4 border-2 border-white/20 border-t-white rounded-full mr-2" />
                            Processing...
                          </>
                        ) : (
                          <>
                            <Sparkles className="h-4 w-4 mr-2" />
                            Apply AI Edit
                          </>
                        )}
                      </Button>
                    )}
                  </div>

                  <div className="text-xs text-text-tertiary">
                    <p>• Select the area you want to modify</p>
                    <p>• Describe your desired changes clearly</p>
                    <p>• AI processing may take 30-60 seconds</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Canvas Area */}
          <div className="flex-1 flex flex-col">
            <div className="flex-1 flex items-center justify-center p-4 bg-gray-900">
              {isLoading ? (
                <div className="flex flex-col items-center space-y-3 text-white">
                  <div className="animate-spin h-8 w-8 border-2 border-green-500 border-t-transparent rounded-full" />
                  <span className="text-lg">Loading image...</span>
                </div>
              ) : canvasError ? (
                <div className="flex flex-col items-center space-y-4 p-6 border border-red-500/30 rounded-lg bg-red-500/10 max-w-md">
                  <div className="text-red-400 text-center">
                    <h3 className="text-lg font-semibold mb-2">Unable to Load Image</h3>
                    <p className="text-sm mb-4">The image editor couldn&apos;t load your image. Please try again or use a different image.</p>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      onClick={() => {
                        setCanvasError(null)
                        setIsLoading(true)
                        // Retry loading
                        window.location.reload()
                      }}
                      size="sm"
                    >
                      Retry
                    </Button>
                    <Button onClick={onClose} variant="secondary" size="sm">
                      Close
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="relative">
                  <canvas
                    ref={canvasRef}
                    className="border border-gray-600 rounded shadow-lg max-w-full max-h-full"
                    style={{ maxWidth: '800px', maxHeight: '450px' }}
                  />
                </div>
              )}
            </div>

            {/* Bottom Actions */}
            <div className="border-t border-border-primary p-4 flex justify-between">
              <div className="flex space-x-2">
                <Button variant="secondary" onClick={handleDownload}>
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
              <div className="flex space-x-2">
                <Button variant="secondary" onClick={onClose}>
                  Cancel
                </Button>
                <Button onClick={handleSave}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
