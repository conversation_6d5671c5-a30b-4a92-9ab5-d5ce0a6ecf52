'use client'

import { useState } from 'react'
import { ImageEditorModal } from '@/components/ImageEditorModal_Working'
import { Button } from '@/components/ui/Button'

const testImages = [
  {
    name: '<PERSON><PERSON> (CORS enabled)',
    url: 'https://picsum.photos/800/450'
  },
  {
    name: 'Unsplash (CORS enabled)',
    url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=450&fit=crop'
  },
  {
    name: 'Base64 Test Image',
    url: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
  }
]

export default function TestEditorPage() {
  const [showEditor, setShowEditor] = useState(false)
  const [currentImageUrl, setCurrentImageUrl] = useState<string>('')
  const [testResults, setTestResults] = useState<Record<string, string>>({})

  const testImageLoad = async (url: string, name: string) => {
    console.log(`Testing image load for ${name}:`, url)
    
    try {
      // Test 1: Direct image load
      const img = new Image()
      img.crossOrigin = 'anonymous'
      
      const directLoadPromise = new Promise<boolean>((resolve) => {
        img.onload = () => resolve(true)
        img.onerror = () => resolve(false)
        img.src = url
      })
      
      const directLoadSuccess = await directLoadPromise
      console.log(`Direct load for ${name}:`, directLoadSuccess)
      
      // Test 2: Fetch test
      let fetchSuccess = false
      try {
        const response = await fetch(url, { mode: 'cors' })
        fetchSuccess = response.ok
      } catch (e) {
        fetchSuccess = false
      }
      console.log(`Fetch test for ${name}:`, fetchSuccess)
      
      const result = `Direct: ${directLoadSuccess ? '✅' : '❌'}, Fetch: ${fetchSuccess ? '✅' : '❌'}`
      setTestResults(prev => ({ ...prev, [name]: result }))
      
    } catch (error) {
      console.error(`Error testing ${name}:`, error)
      setTestResults(prev => ({ ...prev, [name]: '❌ Error' }))
    }
  }

  const openEditor = (url: string) => {
    setCurrentImageUrl(url)
    setShowEditor(true)
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Image Editor Test Page</h1>
        
        <div className="space-y-6">
          {testImages.map((image, index) => (
            <div key={index} className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-4">{image.name}</h3>
              <p className="text-gray-400 mb-4 break-all">{image.url}</p>
              
              <div className="flex space-x-4 mb-4">
                <Button
                  onClick={() => testImageLoad(image.url, image.name)}
                  variant="secondary"
                >
                  Test Load
                </Button>
                
                <Button
                  onClick={() => openEditor(image.url)}
                  className="bg-green-600 hover:bg-green-700"
                >
                  Open in Editor
                </Button>
              </div>
              
              {testResults[image.name] && (
                <div className="text-sm text-gray-300">
                  Test Result: {testResults[image.name]}
                </div>
              )}
            </div>
          ))}
        </div>
        
        <div className="mt-8 bg-gray-800 p-6 rounded-lg">
          <h3 className="text-xl font-semibold mb-4">Custom URL Test</h3>
          <div className="flex space-x-4">
            <input
              type="text"
              placeholder="Enter image URL..."
              className="flex-1 px-4 py-2 bg-gray-700 border border-gray-600 rounded text-white"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  const url = (e.target as HTMLInputElement).value
                  if (url) openEditor(url)
                }
              }}
            />
            <Button
              onClick={() => {
                const input = document.querySelector('input[type="text"]') as HTMLInputElement
                if (input?.value) openEditor(input.value)
              }}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Test Custom URL
            </Button>
          </div>
        </div>
      </div>

      {/* Image Editor Modal */}
      <ImageEditorModal
        isOpen={showEditor}
        onClose={() => setShowEditor(false)}
        imageUrl={currentImageUrl}
        onSave={(editedImageUrl) => {
          console.log('Saved edited image:', editedImageUrl.substring(0, 100) + '...')
          setShowEditor(false)
        }}
      />
    </div>
  )
}
