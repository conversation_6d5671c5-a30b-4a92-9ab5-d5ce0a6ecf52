'use client'

import { useState, useEffect, useRef } from 'react'

export default function SimpleEditorTest() {
  const [showModal, setShowModal] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [Konva, setKonva] = useState<any>(null)
  const stageRef = useRef<HTMLDivElement>(null)

  const testImageUrl = 'https://picsum.photos/400/300'

  // Load Konva
  useEffect(() => {
    const loadKonva = async () => {
      try {
        console.log('Loading Konva...')
        const konvaModule = await import('konva')
        const konvaInstance = konvaModule.default
        console.log('Konva loaded:', typeof konvaInstance)
        setKonva(konvaInstance)
      } catch (error) {
        console.error('Failed to load Konva:', error)
        setError('Failed to load Konva')
      }
    }

    if (!Konva) {
      loadKonva()
    }
  }, [Konva])

  // Initialize when modal opens
  useEffect(() => {
    if (!showModal || !Konva || !stageRef.current) {
      console.log('Conditions not met:', { showModal, hasKonva: !!Konva, hasRef: !!stageRef.current })
      return
    }

    const init = async () => {
      try {
        console.log('=== SIMPLE TEST INITIALIZATION ===')
        setIsLoading(true)
        setError(null)

        // Create stage
        console.log('Creating stage...')
        const stage = new Konva.Stage({
          container: stageRef.current,
          width: 400,
          height: 300
        })

        const layer = new Konva.Layer()
        stage.add(layer)
        console.log('Stage and layer created')

        // Test 1: Just add a simple rectangle first
        console.log('Adding test rectangle...')
        const rect = new Konva.Rect({
          x: 50,
          y: 50,
          width: 100,
          height: 100,
          fill: 'red'
        })
        layer.add(rect)
        layer.draw()
        console.log('Rectangle added')

        // Test 2: Load image with HTML Image
        console.log('Loading image...')
        const img = new Image()
        img.crossOrigin = 'anonymous'
        
        img.onload = () => {
          console.log('Image loaded, adding to canvas...')
          
          const konvaImage = new Konva.Image({
            x: 0,
            y: 0,
            image: img,
            width: 200,
            height: 150
          })
          
          layer.add(konvaImage)
          layer.draw()
          
          setIsLoading(false)
          console.log('=== SIMPLE TEST COMPLETE ===')
        }
        
        img.onerror = (error) => {
          console.error('Image load error:', error)
          setIsLoading(false)
          setError('Image load failed')
        }
        
        img.src = testImageUrl

      } catch (error) {
        console.error('Initialization error:', error)
        setError(`Init error: ${error}`)
        setIsLoading(false)
      }
    }

    init()

    return () => {
      console.log('Cleanup...')
      if (stageRef.current) {
        stageRef.current.innerHTML = ''
      }
    }
  }, [showModal, Konva])

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Simple Editor Test</h1>
        
        <div className="space-y-4">
          <button
            onClick={() => setShowModal(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Open Simple Editor
          </button>
          
          <div className="bg-gray-800 p-4 rounded">
            <h3 className="font-semibold mb-2">Debug Info:</h3>
            <div className="text-sm space-y-1">
              <div>Konva loaded: {Konva ? 'Yes' : 'No'}</div>
              <div>Modal open: {showModal ? 'Yes' : 'No'}</div>
              <div>Loading: {isLoading ? 'Yes' : 'No'}</div>
              <div>Error: {error || 'None'}</div>
            </div>
          </div>
        </div>

        {/* Simple Modal */}
        {showModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80">
            <div className="bg-gray-800 p-6 rounded-lg">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Simple Editor</h2>
                <button
                  onClick={() => setShowModal(false)}
                  className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700"
                >
                  Close
                </button>
              </div>
              
              <div className="mb-4">
                <div>Status: {isLoading ? 'Loading...' : error ? `Error: ${error}` : 'Ready'}</div>
              </div>
              
              <div 
                ref={stageRef}
                className="border border-gray-600 bg-white"
                style={{ width: '400px', height: '300px' }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
