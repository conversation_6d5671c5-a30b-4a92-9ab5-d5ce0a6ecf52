'use client'

import { useEffect, useState } from 'react'

export default function ToastTestPage() {
  const [status, setStatus] = useState('Loading...')
  const [toastUI, setToastUI] = useState<any>(null)

  useEffect(() => {
    const loadToastUI = async () => {
      try {
        console.log('Attempting to load Toast UI Image Editor...')
        setStatus('Loading Toast UI Image Editor...')
        
        // Try to import the module
        const module = await import('tui-image-editor')
        console.log('Module loaded:', module)
        
        const ImageEditor = module.default || module
        console.log('ImageEditor constructor:', typeof ImageEditor)
        
        setToastUI(ImageEditor)
        setStatus('Toast UI Image Editor loaded successfully!')
        
      } catch (error) {
        console.error('Failed to load Toast UI:', error)
        setStatus(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    loadToastUI()
  }, [])

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Toast UI Image Editor Test</h1>
        
        <div className="bg-gray-800 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Load Status:</h2>
          <p className="text-gray-300 mb-4">{status}</p>
          
          <div className="space-y-2 text-sm">
            <p>Toast UI Available: {toastUI ? 'Yes' : 'No'}</p>
            <p>Toast UI Type: {typeof toastUI}</p>
            {toastUI && (
              <p>Constructor Name: {toastUI.name || 'Unknown'}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
