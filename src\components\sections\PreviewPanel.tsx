'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { ZoomIn, Download, Share2, <PERSON>rkles, RotateCcw, Edit3, <PERSON>lette, User, ChevronDown, Wand2, Stars } from 'lucide-react'
import Image from 'next/image'
import { ImageEditorModal } from '@/components/ImageEditorModal_Konva'

interface PreviewPanelProps {
  isGenerating: boolean
  generatedImage: string | null
  prompt: string
  onPromptChange: (prompt: string) => void
  selectedPersona: any // Changed to any to match the actual persona object
  onPersonaChange: (persona: string | null) => void
  selectedInspiration: string | null
  onInspirationChange: (inspiration: string | null) => void
  onGenerate: () => void
  selectedStyle?: any
  speedTier?: 'fast' | 'balanced' | 'quality'
}

// Mock data
const personas = [
  { id: null, name: 'No Persona', icon: '🚫' },
  { id: 'persona-1', name: 'Main Creator', icon: '👤' },
  { id: 'persona-2', name: 'Gaming Avatar', icon: '🎮' }
]

const inspirations = [
  { id: null, name: 'Original Style', icon: '✨' },
  { id: 'style-1', name: 'Gaming Red/Blue', icon: '🎮' },
  { id: 'style-2', name: 'Tech Minimal', icon: '💻' },
  { id: 'style-3', name: 'Food Warm', icon: '🍕' }
]

export function PreviewPanel({ 
  isGenerating, 
  generatedImage, 
  prompt, 
  onPromptChange,
  selectedPersona,
  onPersonaChange,
  selectedInspiration,
  onInspirationChange,
  onGenerate,
  selectedStyle,
  speedTier = 'balanced'
}: PreviewPanelProps) {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)

  const handleSaveEdit = (editedThumbnail: string) => {
    console.log('Saving edited thumbnail:', editedThumbnail)
    setIsEditModalOpen(false)
  }

  const thumbnailForEdit = generatedImage || '/demo-earth-thumbnail.svg'

  const speedTierInfo = {
    fast: {
      icon: '⚡',
      label: 'Fast',
      time: '2-4 seconds',
      cost: '1 credit',
      description: 'Quick generation with good quality'
    },
    balanced: {
      icon: '⚖️',
      label: 'Balanced',
      time: '6-12 seconds',
      cost: '2 credits',
      description: 'Best balance of speed and quality'
    },
    quality: {
      icon: '💎',
      label: 'Quality',
      time: '15-25 seconds',
      cost: '3 credits',
      description: 'Maximum quality and detail'
    }
  }

  const currentSpeedInfo = speedTierInfo[speedTier]

  return (
    <>
      <div className="relative overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute -top-40 -right-40 h-80 w-80 rounded-full bg-gradient-to-br from-accent-primary/20 to-purple-500/20 blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 h-80 w-80 rounded-full bg-gradient-to-br from-cyan-500/20 to-accent-primary/20 blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div className="relative z-10 p-8">
          {/* Header */}
          <div className="mb-8 flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-accent-primary to-purple-600 shadow-lg">
                <Wand2 className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-2xl font-bold bg-gradient-to-r from-text-primary to-accent-primary bg-clip-text text-transparent">
                  Create Thumbnail
                </h3>
                <p className="text-sm text-text-secondary">AI-powered thumbnail generation</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" className="hover:bg-white/10 transition-all duration-200">
                <ZoomIn className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" className="hover:bg-white/10 transition-all duration-200">
                <Download className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" className="hover:bg-white/10 transition-all duration-200">
                <Share2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Controls */}
          <div className="mb-8 space-y-6">
            {/* Prompt Input */}
            <div className="group">
              <label className="mb-3 flex items-center space-x-2 text-sm font-semibold text-text-primary">
                <Stars className="h-4 w-4 text-white" />
                <span>Describe your thumbnail</span>
              </label>
              <div className="relative">
                <textarea
                  value={prompt}
                  onChange={(e) => onPromptChange(e.target.value)}
                  placeholder="A gaming setup with RGB lighting and multiple monitors, epic cinematic style..."
                  className="h-24 w-full resize-none rounded-xl border border-border-primary/50 bg-transparent p-4 text-sm text-text-primary placeholder-text-tertiary transition-all duration-200 focus:border-accent-primary focus:outline-none focus:ring-2 focus:ring-accent-primary/20 group-hover:border-border-secondary"
                />
                <div className="absolute bottom-3 right-3 text-xs text-text-tertiary">
                  {prompt.length}/500
                </div>
              </div>
            </div>

            {/* Persona and Inspiration Dropdowns */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Persona Dropdown */}
              <div className="group">
                <label className="mb-3 flex items-center space-x-2 text-sm font-semibold text-text-primary">
                  <User className="h-4 w-4 text-white" />
                  <span>Persona</span>
                </label>
                <div className="relative">
                  <select
                    value={selectedPersona || ''}
                    onChange={(e) => onPersonaChange(e.target.value || null)}
                    className="w-full appearance-none rounded-xl border border-border-primary/50 bg-transparent p-4 pr-12 text-sm text-text-primary transition-all duration-200 focus:border-accent-primary focus:outline-none focus:ring-2 focus:ring-accent-primary/20 group-hover:border-border-secondary"
                  >
                    {personas.map((persona) => (
                      <option key={persona.id || 'none'} value={persona.id || ''}>
                        {persona.icon} {persona.name}
                      </option>
                    ))}
                  </select>
                  <div className="absolute right-4 top-1/2 -translate-y-1/2 pointer-events-none">
                    <ChevronDown className="h-4 w-4 text-white transition-transform duration-200" />
                  </div>
                </div>
              </div>

              {/* Inspiration Dropdown */}
              <div className="group">
                <label className="mb-3 flex items-center space-x-2 text-sm font-semibold text-text-primary">
                  <Palette className="h-4 w-4 text-white" />
                  <span>Style Inspiration</span>
                </label>
                <div className="relative">
                  <select
                    value={selectedInspiration || ''}
                    onChange={(e) => onInspirationChange(e.target.value || null)}
                    className="w-full appearance-none rounded-xl border border-border-primary/50 bg-transparent p-4 pr-12 text-sm text-text-primary transition-all duration-200 focus:border-accent-primary focus:outline-none focus:ring-2 focus:ring-accent-primary/20 group-hover:border-border-secondary"
                  >
                    {inspirations.map((inspiration) => (
                      <option key={inspiration.id || 'none'} value={inspiration.id || ''}>
                        {inspiration.icon} {inspiration.name}
                      </option>
                    ))}
                  </select>
                  <div className="absolute right-4 top-1/2 -translate-y-1/2 pointer-events-none">
                    <ChevronDown className="h-4 w-4 text-white transition-transform duration-200" />
                  </div>
                </div>
              </div>
            </div>

            {/* Generate Button */}
            <Button
              onClick={onGenerate}
              disabled={!prompt.trim() || isGenerating}
              className="group relative w-full overflow-hidden rounded-xl bg-gradient-to-r from-accent-primary via-accent-secondary to-accent-primary bg-size-200 p-6 text-lg font-semibold shadow-lg transition-all duration-300 hover:bg-pos-100 hover:shadow-xl disabled:opacity-50"
              size="lg"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>
              {isGenerating ? (
                <>
                  <div className="mr-3 h-5 w-5 animate-spin rounded-full border-2 border-white/30 border-t-white" />
                  Generating Magic...
                </>
              ) : (
                <>
                  <Sparkles className="mr-3 h-5 w-5 animate-pulse" />
                  Generate Thumbnail (2 credits)
                </>
              )}
            </Button>
          </div>

          {/* Preview Container */}
          <div className="relative overflow-hidden rounded-xl">
            <div className="aspect-video w-full">
              {/* Loading State */}
              {isGenerating && (
                <div className="flex h-full items-center justify-center bg-gradient-to-br from-bg-tertiary to-bg-primary">
                  <div className="text-center">
                    <div className="relative mx-auto mb-6">
                      <div className="h-16 w-16 animate-spin rounded-full border-4 border-border-primary border-t-accent-primary"></div>
                      <div className="absolute inset-0 h-16 w-16 animate-ping rounded-full border-4 border-accent-primary/20"></div>
                    </div>
                    <p className="mb-4 text-lg font-semibold text-text-primary">
                      Creating your masterpiece...
                    </p>
                    <div className="mx-auto mb-3 h-2 w-80 overflow-hidden rounded-full bg-bg-primary/50">
                      <div className="h-full bg-gradient-to-r from-accent-primary to-accent-secondary transition-all duration-300 ease-out animate-pulse" style={{ width: '45%' }}></div>
                    </div>
                    <p className="text-sm text-text-secondary">45% complete • Applying AI magic</p>
                  </div>
                </div>
              )}

              {/* Generated Result */}
              {!isGenerating && generatedImage && (
                <div className="group relative h-full w-full">
                  <Image
                    src={generatedImage}
                    alt="Generated Thumbnail"
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  
                  {/* Interactive Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 transition-all duration-300 group-hover:opacity-100">
                    <div className="flex h-full flex-col items-center justify-center space-y-6">
                      {/* Quality Indicator */}
                      <div className="rounded-xl bg-white/10 px-4 py-3 backdrop-blur-md border border-white/20">
                        <div className="text-center">
                          <span className="text-3xl font-bold text-white">98%</span>
                          <p className="text-sm text-white/80">Quality Score</p>
                        </div>
                      </div>

                      {/* Quick Actions */}
                      <div className="flex space-x-3">
                        <Button size="sm" className="bg-white/20 text-white hover:bg-white/30 backdrop-blur-md border border-white/20 transition-all duration-200">
                          <Sparkles className="mr-2 h-4 w-4" />
                          Enhance
                        </Button>
                        <Button size="sm" className="bg-white/20 text-white hover:bg-white/30 backdrop-blur-md border border-white/20 transition-all duration-200">
                          <RotateCcw className="mr-2 h-4 w-4" />
                          Regenerate
                        </Button>
                        <Button 
                          size="sm" 
                          className="bg-white/20 text-white hover:bg-white/30 backdrop-blur-md border border-white/20 transition-all duration-200"
                          onClick={() => setIsEditModalOpen(true)}
                        >
                          <Edit3 className="mr-2 h-4 w-4" />
                          Edit Areas
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Placeholder State */}
              {!isGenerating && !generatedImage && (
                <div className="flex h-full items-center justify-center">
                  <div className="text-center max-w-md px-6">
                    <div className="relative mx-auto mb-6">
                      <div className="h-20 w-20 rounded-full bg-gradient-to-br from-accent-primary/20 to-accent-secondary/20 flex items-center justify-center">
                        <Palette className="h-10 w-10 text-white animate-pulse" />
                      </div>
                      <div className="absolute -top-2 -right-2 h-6 w-6 rounded-full bg-gradient-to-br from-green-400 to-green-500 flex items-center justify-center">
                        <Sparkles className="h-3 w-3 text-white" />
                      </div>
                    </div>
                    <h4 className="mb-3 text-xl font-bold text-text-primary">
                      Ready to Create Magic?
                    </h4>
                    <p className="mb-6 text-sm text-text-secondary leading-relaxed">
                      {prompt 
                        ? "Your vision is ready! Click the generate button to bring your thumbnail to life with AI."
                        : "Describe your perfect thumbnail above and watch our AI transform your words into stunning visuals."
                      }
                    </p>
                    
                    {/* Demo Edit Areas Button */}
                    {prompt && (
                      <Button 
                        className="bg-gradient-to-r from-accent-primary/20 to-accent-secondary/20 border border-accent-primary/30 hover:from-accent-primary/30 hover:to-accent-secondary/30 transition-all duration-200" 
                        variant="outline"
                        onClick={() => setIsEditModalOpen(true)}
                      >
                        <Edit3 className="mr-2 h-4 w-4" />
                        Try Edit Areas (Demo)
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Image Editor Modal */}
      <ImageEditorModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        imageUrl={thumbnailForEdit}
        onSave={handleSaveEdit}
      />

      {/* Speed Tier Info */}
      <div className="p-4">
        <div className="flex items-center space-x-3 mb-3">
          <div className="text-2xl">{currentSpeedInfo.icon}</div>
          <div className="flex-1">
            <div className="text-sm font-medium text-text-primary">
              {currentSpeedInfo.label} Mode
            </div>
            <div className="text-xs text-text-secondary">
              {currentSpeedInfo.description}
            </div>
          </div>
          <div className="text-right">
            <div className="text-xs font-medium text-accent-primary">
              {currentSpeedInfo.cost}
            </div>
            <div className="text-xs text-text-secondary">
              {currentSpeedInfo.time}
            </div>
          </div>
        </div>
      </div>

      {/* Generation Summary */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-text-primary">Generation Summary</h4>
        
        {/* Prompt Preview */}
        <div className="p-3 bg-bg-tertiary rounded-lg border border-border-primary">
          <div className="text-xs text-text-secondary mb-1">Prompt</div>
          <div className="text-sm text-text-primary">
            {prompt || 'Enter a prompt to get started...'}
          </div>
        </div>

        {/* Selected Persona */}
        {selectedPersona && (
          <div className="p-3 bg-bg-tertiary rounded-lg border border-border-primary">
            <div className="text-xs text-text-secondary mb-1">Persona</div>
            <div className="flex items-center space-x-2">
              {selectedPersona.image_base64 && (
                <img 
                  src={`data:image/jpeg;base64,${selectedPersona.image_base64}`}
                  alt={selectedPersona.name}
                  className="w-6 h-6 rounded-full object-cover"
                />
              )}
              <span className="text-sm text-text-primary">{selectedPersona.name}</span>
              {selectedPersona.loraTraining?.status === 'completed' && (
                <span className="text-xs px-2 py-1 bg-green-500/20 text-green-400 rounded">
                  LoRA Ready
                </span>
              )}
            </div>
          </div>
        )}

        {/* Selected Style */}
        {selectedStyle && (
          <div className="p-3 bg-bg-tertiary rounded-lg border border-border-primary">
            <div className="text-xs text-text-secondary mb-1">Style</div>
            <div className="flex items-center space-x-2">
              <div 
                className="w-6 h-6 rounded"
                style={{ backgroundColor: selectedStyle.primaryColor || '#6366f1' }}
              />
              <span className="text-sm text-text-primary">{selectedStyle.name}</span>
            </div>
          </div>
        )}

        {/* Estimated Cost & Time */}
        <div className="grid grid-cols-2 gap-3">
          <div className="p-3 bg-bg-tertiary rounded-lg border border-border-primary text-center">
            <div className="text-xs text-text-secondary mb-1">Cost</div>
            <div className="text-lg font-semibold text-accent-primary">
              {currentSpeedInfo.cost}
            </div>
          </div>
          <div className="p-3 bg-bg-tertiary rounded-lg border border-border-primary text-center">
            <div className="text-xs text-text-secondary mb-1">Time</div>
            <div className="text-lg font-semibold text-text-primary">
              {currentSpeedInfo.time}
            </div>
          </div>
        </div>

        {/* Generation Tips */}
        <div className="p-3 bg-accent-primary/10 rounded-lg border border-accent-primary/20">
          <div className="text-xs font-medium text-accent-primary mb-1">💡 Tip</div>
          <div className="text-xs text-text-secondary">
            {speedTier === 'fast' ? 
              'Fast mode uses FLUX-Juiced for 2.6x faster generation with great quality.' :
             speedTier === 'quality' ?
              'Quality mode uses FLUX Pro for maximum detail and professional results.' :
              'Balanced mode offers the best compromise between speed and quality.'
            }
          </div>
        </div>
      </div>
    </>
  )
} 