'use client'

import { useEffect, useRef, useState } from 'react'

export default function FabricTestPage() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [status, setStatus] = useState('Initializing...')
  const [fabric, setFabric] = useState<any>(null)

  useEffect(() => {
    const loadFabric = async () => {
      try {
        console.log('Loading Fabric.js...')
        setStatus('Loading Fabric.js...')
        
        // Dynamic import of Fabric.js
        const fabricModule = await import('fabric')
        const fabricInstance = fabricModule.fabric
        
        console.log('Fabric.js loaded:', typeof fabricInstance)
        setFabric(fabricInstance)
        setStatus('Fabric.js loaded successfully')
        
        if (canvasRef.current) {
          console.log('Creating canvas...')
          setStatus('Creating canvas...')
          
          const canvas = new fabricInstance.Canvas(canvasRef.current, {
            width: 400,
            height: 300,
            backgroundColor: '#f0f0f0'
          })
          
          console.log('Canvas created:', canvas)
          setStatus('Canvas created successfully')
          
          // Test adding a simple rectangle
          const rect = new fabricInstance.Rect({
            left: 50,
            top: 50,
            width: 100,
            height: 100,
            fill: 'red'
          })
          
          canvas.add(rect)
          canvas.renderAll()
          setStatus('Test rectangle added')
          
          // Test loading an image
          console.log('Testing image load...')
          setStatus('Testing image load...')
          
          const testImageUrl = 'https://picsum.photos/200/150'
          
          fabricInstance.Image.fromURL(testImageUrl, (img: any) => {
            if (img) {
              console.log('Image loaded successfully')
              img.set({
                left: 200,
                top: 50,
                scaleX: 0.5,
                scaleY: 0.5
              })
              canvas.add(img)
              canvas.renderAll()
              setStatus('Image loaded and added to canvas')
            } else {
              console.error('Failed to load image')
              setStatus('Failed to load test image')
            }
          }, { crossOrigin: 'anonymous' })
        }
        
      } catch (error) {
        console.error('Error loading Fabric.js:', error)
        setStatus(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    loadFabric()
  }, [])

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Fabric.js Test Page</h1>
        
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Status:</h2>
          <p className="text-gray-300">{status}</p>
        </div>
        
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Test Canvas:</h3>
          <canvas
            ref={canvasRef}
            className="border border-gray-600 rounded"
            style={{ maxWidth: '100%' }}
          />
        </div>
        
        <div className="mt-6 bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Debug Info:</h3>
          <div className="space-y-2 text-sm text-gray-300">
            <p>Fabric.js available: {fabric ? 'Yes' : 'No'}</p>
            <p>Canvas ref: {canvasRef.current ? 'Available' : 'Not available'}</p>
            <p>Window object: {typeof window !== 'undefined' ? 'Available' : 'Not available'}</p>
          </div>
        </div>
      </div>
    </div>
  )
}
