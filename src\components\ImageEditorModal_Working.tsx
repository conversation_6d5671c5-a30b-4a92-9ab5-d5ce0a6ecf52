'use client'

import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from './ui/Button'
import { X, Download, Save, Type, Plus } from 'lucide-react'

interface ImageEditorModalProps {
  isOpen: boolean
  onClose: () => void
  imageUrl: string
  onSave: (editedImageUrl: string) => void
}

export function ImageEditorModal({ isOpen, onClose, imageUrl, onSave }: ImageEditorModalProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isReady, setIsReady] = useState(false)
  const [loadedImage, setLoadedImage] = useState<HTMLImageElement | null>(null)
  const [textElements, setTextElements] = useState<Array<{
    text: string
    x: number
    y: number
    fontSize: number
    color: string
    id: string
  }>>([])
  
  // Text controls
  const [textInput, setTextInput] = useState('')
  const [textColor, setTextColor] = useState('#ffffff')
  const [fontSize, setFontSize] = useState(40)

  // Simple, proven image loading (based on debug success)
  useEffect(() => {
    if (!isOpen) {
      setIsReady(false)
      setLoadedImage(null)
      setTextElements([])
      return
    }

    const loadImage = async () => {
      console.log('🚀 Loading image for editor...')
      
      try {
        // Use the same proven method from debug test
        const img = new Image()
        img.crossOrigin = 'anonymous'
        
        await new Promise<void>((resolve, reject) => {
          img.onload = () => {
            console.log('✅ Image loaded for editor:', img.width, 'x', img.height)
            setLoadedImage(img)
            setIsReady(true)
            resolve()
          }
          
          img.onerror = (error) => {
            console.error('❌ Image load failed:', error)
            reject(error)
          }
          
          img.src = imageUrl
        })
        
      } catch (error) {
        console.error('Failed to load image:', error)
        setIsReady(false)
      }
    }

    loadImage()
  }, [isOpen, imageUrl])

  // Draw to canvas when image or text changes
  useEffect(() => {
    if (!isReady || !loadedImage || !canvasRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size
    canvas.width = 800
    canvas.height = 450

    // Calculate image scaling
    const scaleX = canvas.width / loadedImage.width
    const scaleY = canvas.height / loadedImage.height
    const scale = Math.min(scaleX, scaleY) * 0.9

    const scaledWidth = loadedImage.width * scale
    const scaledHeight = loadedImage.height * scale
    const x = (canvas.width - scaledWidth) / 2
    const y = (canvas.height - scaledHeight) / 2

    // Clear and draw background
    ctx.fillStyle = '#000000'
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // Draw image
    ctx.drawImage(loadedImage, x, y, scaledWidth, scaledHeight)

    // Draw text elements
    textElements.forEach(textEl => {
      ctx.font = `${textEl.fontSize}px Arial`
      ctx.fillStyle = textEl.color
      ctx.textAlign = 'left'
      ctx.textBaseline = 'top'
      
      // Add text shadow for better visibility
      ctx.shadowColor = 'rgba(0, 0, 0, 0.8)'
      ctx.shadowBlur = 4
      ctx.shadowOffsetX = 2
      ctx.shadowOffsetY = 2
      
      ctx.fillText(textEl.text, textEl.x, textEl.y)
      
      // Reset shadow
      ctx.shadowColor = 'transparent'
      ctx.shadowBlur = 0
      ctx.shadowOffsetX = 0
      ctx.shadowOffsetY = 0
    })
  }, [isReady, loadedImage, textElements])

  const addText = () => {
    if (!textInput.trim()) return

    const newText = {
      text: textInput,
      x: 100 + (textElements.length * 20), // Offset each new text
      y: 100 + (textElements.length * 50),
      fontSize: fontSize,
      color: textColor,
      id: Date.now().toString()
    }

    setTextElements(prev => [...prev, newText])
    setTextInput('')
  }

  const clearText = () => {
    setTextElements([])
  }

  const handleSave = () => {
    if (!canvasRef.current) return

    try {
      const dataURL = canvasRef.current.toDataURL('image/png', 0.9)
      console.log('💾 Saving edited image...')
      onSave(dataURL)
      onClose()
    } catch (error) {
      console.error('Error saving:', error)
    }
  }

  const handleDownload = () => {
    if (!canvasRef.current) return

    try {
      const dataURL = canvasRef.current.toDataURL('image/png', 0.9)
      const link = document.createElement('a')
      link.download = 'edited-image.png'
      link.href = dataURL
      link.click()
    } catch (error) {
      console.error('Error downloading:', error)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm">
      <div className="bg-bg-secondary border border-border-primary rounded-lg w-[95vw] h-[90vh] max-w-6xl flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border-primary">
          <h2 className="text-xl font-semibold text-text-primary">Image Editor</h2>
          <div className="flex items-center space-x-2">
            {isReady && (
              <>
                <Button
                  onClick={handleDownload}
                  variant="secondary"
                  size="sm"
                  className="flex items-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>Download</span>
                </Button>
                <Button
                  onClick={handleSave}
                  size="sm"
                  className="flex items-center space-x-2 bg-green-600 hover:bg-green-700"
                >
                  <Save className="w-4 h-4" />
                  <span>Save</span>
                </Button>
              </>
            )}
            <Button
              onClick={onClose}
              variant="secondary"
              size="sm"
              className="flex items-center space-x-2"
            >
              <X className="w-4 h-4" />
              <span>Close</span>
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 flex">
          {/* Sidebar */}
          {isReady && (
            <div className="w-80 border-r border-border-primary p-4 space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-text-primary flex items-center space-x-2">
                  <Type className="w-5 h-5" />
                  <span>Add Text</span>
                </h3>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Text
                  </label>
                  <input
                    type="text"
                    value={textInput}
                    onChange={(e) => setTextInput(e.target.value)}
                    placeholder="Enter text..."
                    className="w-full px-3 py-2 bg-bg-primary border border-border-primary rounded text-text-primary"
                    onKeyPress={(e) => e.key === 'Enter' && addText()}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Font Size: {fontSize}px
                  </label>
                  <input
                    type="range"
                    min="12"
                    max="100"
                    value={fontSize}
                    onChange={(e) => setFontSize(Number(e.target.value))}
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Color
                  </label>
                  <input
                    type="color"
                    value={textColor}
                    onChange={(e) => setTextColor(e.target.value)}
                    className="w-full h-10 rounded border border-border-primary"
                  />
                </div>

                <div className="flex space-x-2">
                  <Button
                    onClick={addText}
                    disabled={!textInput.trim()}
                    className="flex-1 flex items-center justify-center space-x-2"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Add Text</span>
                  </Button>
                  
                  <Button
                    onClick={clearText}
                    variant="secondary"
                    className="flex items-center space-x-2"
                  >
                    <span>Clear</span>
                  </Button>
                </div>

                {textElements.length > 0 && (
                  <div className="text-sm text-text-secondary">
                    {textElements.length} text element(s) added
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Canvas Area */}
          <div className="flex-1 flex items-center justify-center p-4">
            {!isReady ? (
              <div className="flex flex-col items-center space-y-4">
                <div className="animate-spin h-8 w-8 border-2 border-green-500 border-t-transparent rounded-full" />
                <span className="text-lg text-text-primary">Loading image...</span>
              </div>
            ) : (
              <canvas
                ref={canvasRef}
                className="border border-border-primary rounded bg-black max-w-full max-h-full"
                style={{ width: '800px', height: '450px' }}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
