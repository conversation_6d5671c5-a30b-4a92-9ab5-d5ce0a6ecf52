'use client'

import { useEffect, useRef, useState } from 'react'

export default function FabricImageTestPage() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [status, setStatus] = useState('Initializing...')
  const [fabric, setFabric] = useState<any>(null)
  const [logs, setLogs] = useState<string[]>([])

  const addLog = (message: string) => {
    console.log(message)
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  useEffect(() => {
    const loadFabric = async () => {
      try {
        addLog('Loading Fabric.js...')
        setStatus('Loading Fabric.js...')
        
        const fabricModule = await import('fabric')
        const fabricInstance = fabricModule.fabric
        
        addLog(`Fabric.js loaded: ${typeof fabricInstance}`)
        setFabric(fabricInstance)
        setStatus('Fabric.js loaded successfully')
        
        if (canvasRef.current) {
          addLog('Creating canvas...')
          setStatus('Creating canvas...')
          
          const canvas = new fabricInstance.Canvas(canvasRef.current, {
            width: 600,
            height: 400,
            backgroundColor: '#f0f0f0'
          })
          
          addLog('Canvas created successfully')
          setStatus('Canvas created successfully')
          
          // Test different image loading methods
          const testImages = [
            {
              name: 'Lorem Picsum (CORS enabled)',
              url: 'https://picsum.photos/300/200'
            },
            {
              name: 'Unsplash (CORS enabled)', 
              url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop'
            }
          ]

          let testIndex = 0
          
          const testNextImage = () => {
            if (testIndex >= testImages.length) {
              addLog('All image tests completed')
              setStatus('All tests completed')
              return
            }

            const testImage = testImages[testIndex]
            addLog(`Testing ${testImage.name}...`)
            setStatus(`Testing ${testImage.name}...`)

            // Method 1: Direct Fabric.js loading
            const startTime = Date.now()
            
            fabricInstance.Image.fromURL(testImage.url, (img: any) => {
              const loadTime = Date.now() - startTime
              
              if (img) {
                addLog(`✅ ${testImage.name} loaded successfully in ${loadTime}ms`)
                
                img.set({
                  left: 50 + (testIndex * 150),
                  top: 50,
                  scaleX: 0.5,
                  scaleY: 0.5
                })
                
                canvas.add(img)
                canvas.renderAll()
                
                testIndex++
                setTimeout(testNextImage, 1000)
              } else {
                addLog(`❌ ${testImage.name} failed to load (no img object)`)
                testIndex++
                setTimeout(testNextImage, 1000)
              }
            }, { 
              crossOrigin: 'anonymous'
            })

            // Timeout for this test
            setTimeout(() => {
              if (Date.now() - startTime > 10000) {
                addLog(`⏰ ${testImage.name} timed out after 10 seconds`)
                testIndex++
                setTimeout(testNextImage, 100)
              }
            }, 10000)
          }

          // Start testing
          setTimeout(testNextImage, 1000)
        }
        
      } catch (error) {
        addLog(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
        setStatus(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    loadFabric()
  }, [])

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Fabric.js Image Loading Test</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <div className="mb-6">
              <h2 className="text-xl font-semibold mb-2">Status:</h2>
              <p className="text-gray-300">{status}</p>
            </div>
            
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-4">Test Canvas:</h3>
              <canvas
                ref={canvasRef}
                className="border border-gray-600 rounded bg-white"
                style={{ maxWidth: '100%' }}
              />
            </div>
          </div>

          <div>
            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-4">Debug Logs:</h3>
              <div className="bg-gray-900 p-4 rounded text-sm font-mono max-h-96 overflow-y-auto">
                {logs.map((log, index) => (
                  <div key={index} className="mb-1 text-gray-300">
                    {log}
                  </div>
                ))}
                {logs.length === 0 && (
                  <div className="text-gray-500">No logs yet...</div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
